#!/bin/bash

# Container Runner Startup Script
set -e

echo "🚀 Starting Container Runner..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 20+ and try again."
        exit 1
    fi
    
    # Check pnpm
    if ! command -v pnpm &> /dev/null; then
        print_error "pnpm is not installed. Please install pnpm and try again."
        exit 1
    fi
    
    # Check Docker
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker and try again."
        exit 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        print_error "Docker daemon is not running. Please start Docker and try again."
        exit 1
    fi
    
    print_success "All prerequisites are met!"
}

# Install dependencies
install_dependencies() {
    print_status "Installing dependencies..."
    pnpm install
    print_success "Dependencies installed!"
}

# Build Docker images
build_docker_images() {
    print_status "Building Docker images..."
    
    cd apps/api/docker
    chmod +x build-images.sh
    
    print_warning "This may take several minutes on first run..."
    ./build-images.sh
    
    cd ../../..
    print_success "Docker images built successfully!"
}

# Check if Docker images exist
check_docker_images() {
    print_status "Checking Docker images..."
    
    if docker images | grep -q "node-desktop-runner"; then
        print_success "Docker images found!"
        return 0
    else
        print_warning "Docker images not found. Building them now..."
        build_docker_images
        return 1
    fi
}

# Start API server
start_api() {
    print_status "Starting API server..."
    
    cd apps/api
    pnpm dev &
    API_PID=$!
    cd ../..
    
    # Wait for API to be ready
    print_status "Waiting for API server to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:3001/health > /dev/null 2>&1; then
            print_success "API server is ready!"
            return 0
        fi
        sleep 1
    done
    
    print_error "API server failed to start"
    return 1
}

# Start frontend
start_frontend() {
    print_status "Starting frontend..."
    
    cd apps/web
    pnpm dev &
    WEB_PID=$!
    cd ../..
    
    # Wait for frontend to be ready
    print_status "Waiting for frontend to be ready..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null 2>&1; then
            print_success "Frontend is ready!"
            return 0
        fi
        sleep 1
    done
    
    print_error "Frontend failed to start"
    return 1
}

# Cleanup function
cleanup() {
    print_status "Shutting down services..."
    
    if [ ! -z "$API_PID" ]; then
        kill $API_PID 2>/dev/null || true
    fi
    
    if [ ! -z "$WEB_PID" ]; then
        kill $WEB_PID 2>/dev/null || true
    fi
    
    print_success "Services stopped!"
}

# Set up signal handlers
trap cleanup EXIT INT TERM

# Main execution
main() {
    echo "=================================="
    echo "🐳 Container Runner Setup"
    echo "=================================="
    
    check_prerequisites
    install_dependencies
    check_docker_images
    
    echo ""
    echo "=================================="
    echo "🚀 Starting Services"
    echo "=================================="
    
    start_api
    start_frontend
    
    echo ""
    echo "=================================="
    echo "✅ Container Runner is Ready!"
    echo "=================================="
    echo ""
    echo "📱 Frontend:  http://localhost:3000"
    echo "🔧 API:       http://localhost:3001"
    echo "📊 Dashboard: http://localhost:3000/dashboard"
    echo ""
    echo "Press Ctrl+C to stop all services"
    echo "=================================="
    
    # Keep the script running
    wait
}

# Run main function
main
