export interface VNCConnectionConfig {
  host: string;
  port: number;
  password?: string;
  path?: string;
  encrypt?: boolean;
  wsProtocols?: string[];
  repeaterID?: string;
  shared?: boolean;
}

export interface VNCViewerOptions {
  viewOnly?: boolean;
  focusOnClick?: boolean;
  clipViewport?: boolean;
  dragViewport?: boolean;
  scaleViewport?: boolean;
  resizeSession?: boolean;
  showDotCursor?: boolean;
  background?: string;
  qualityLevel?: number;
  compressionLevel?: number;
}

export interface VNCViewerState {
  connected: boolean;
  connecting: boolean;
  disconnected: boolean;
  error: string | null;
  rfbVersion: string | null;
  serverName: string | null;
}

export interface VNCCredentials {
  username?: string;
  password?: string;
  target?: string;
}

export interface VNCViewerEvents {
  onConnect?: () => void;
  onDisconnect?: (clean: boolean) => void;
  onCredentialsRequired?: (types: string[]) => void;
  onSecurityFailure?: (retryCount: number, types: string[]) => void;
  onClipboard?: (text: string) => void;
  onBell?: () => void;
  onDesktopName?: (name: string) => void;
  onCapabilities?: (capabilities: any) => void;
}

export interface VNCViewerProps extends VNCViewerOptions, VNCViewerEvents {
  url: string;
  credentials?: VNCCredentials;
  className?: string;
  style?: React.CSSProperties;
  width?: number;
  height?: number;
  autoConnect?: boolean;
  reconnectDelay?: number;
  maxReconnectAttempts?: number;
}

export interface VNCViewerRef {
  connect: () => void;
  disconnect: () => void;
  sendCredentials: (credentials: VNCCredentials) => void;
  sendKey: (keysym: number, code: string, down?: boolean) => void;
  sendCtrlAltDel: () => void;
  clipboardPasteFrom: (text: string) => void;
  machineShutdown: () => void;
  machineReboot: () => void;
  machineReset: () => void;
  getState: () => VNCViewerState;
}
