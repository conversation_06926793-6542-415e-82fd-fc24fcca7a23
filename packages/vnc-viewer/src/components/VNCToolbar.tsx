"use client"

import React, { useState } from 'react';
import type { VNCViewerRef, VNCViewerState } from '../types/index.js';

interface VNCToolbarProps {
  vncRef: React.RefObject<VNCViewerRef | null>;
  state: VNCViewerState;
  className?: string;
  style?: React.CSSProperties;
  showFullscreen?: boolean;
  showClipboard?: boolean;
  showPower?: boolean;
  showKeyboard?: boolean;
}

export function VNCToolbar({
  vncRef,
  state,
  className = '',
  style,
  showFullscreen = true,
  showClipboard = true,
  showPower = true,
  showKeyboard = true,
}: VNCToolbarProps) {
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [clipboardText, setClipboardText] = useState('');
  const [showClipboardDialog, setShowClipboardDialog] = useState(false);

  const handleCtrlAltDel = () => {
    vncRef.current?.sendCtrlAltDel();
  };

  const handleFullscreen = () => {
    const element = document.documentElement;
    
    if (!isFullscreen) {
      if (element.requestFullscreen) {
        element.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    
    setIsFullscreen(!isFullscreen);
  };

  const handleClipboardPaste = () => {
    if (clipboardText.trim()) {
      vncRef.current?.clipboardPasteFrom(clipboardText);
      setShowClipboardDialog(false);
      setClipboardText('');
    }
  };

  const handleShutdown = () => {
    if (confirm('Are you sure you want to shutdown the remote machine?')) {
      vncRef.current?.machineShutdown();
    }
  };

  const handleReboot = () => {
    if (confirm('Are you sure you want to reboot the remote machine?')) {
      vncRef.current?.machineReboot();
    }
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset the remote machine?')) {
      vncRef.current?.machineReset();
    }
  };

  const toolbarStyle: React.CSSProperties = {
    display: 'flex',
    alignItems: 'center',
    gap: '0.5rem',
    padding: '0.5rem',
    backgroundColor: '#f5f5f5',
    borderBottom: '1px solid #ddd',
    ...style,
  };

  const buttonStyle: React.CSSProperties = {
    padding: '0.5rem',
    border: '1px solid #ccc',
    backgroundColor: 'white',
    borderRadius: '4px',
    cursor: 'pointer',
    fontSize: '0.8rem',
    display: 'flex',
    alignItems: 'center',
    gap: '0.25rem',
  };

  const disabledButtonStyle: React.CSSProperties = {
    ...buttonStyle,
    opacity: 0.5,
    cursor: 'not-allowed',
  };

  return (
    <>
      <div className={`vnc-toolbar ${className}`} style={toolbarStyle}>
        {/* Connection Status */}
        <div style={{ marginRight: '1rem', fontSize: '0.8rem' }}>
          <span
            style={{
              display: 'inline-block',
              width: '8px',
              height: '8px',
              borderRadius: '50%',
              backgroundColor: state.connected ? '#27ae60' : '#e74c3c',
              marginRight: '0.5rem',
            }}
          />
          {state.connected ? 'Connected' : 'Disconnected'}
        </div>

        {/* Keyboard Shortcuts */}
        {showKeyboard && (
          <button
            onClick={handleCtrlAltDel}
            disabled={!state.connected}
            style={state.connected ? buttonStyle : disabledButtonStyle}
            title="Send Ctrl+Alt+Del"
          >
            ⌨️ Ctrl+Alt+Del
          </button>
        )}

        {/* Clipboard */}
        {showClipboard && (
          <button
            onClick={() => setShowClipboardDialog(true)}
            disabled={!state.connected}
            style={state.connected ? buttonStyle : disabledButtonStyle}
            title="Paste from clipboard"
          >
            📋 Clipboard
          </button>
        )}

        {/* Fullscreen */}
        {showFullscreen && (
          <button
            onClick={handleFullscreen}
            style={buttonStyle}
            title="Toggle fullscreen"
          >
            {isFullscreen ? '🗗' : '🗖'} Fullscreen
          </button>
        )}

        {/* Power Options */}
        {showPower && (
          <>
            <button
              onClick={handleReboot}
              disabled={!state.connected}
              style={state.connected ? buttonStyle : disabledButtonStyle}
              title="Reboot machine"
            >
              🔄 Reboot
            </button>
            <button
              onClick={handleShutdown}
              disabled={!state.connected}
              style={state.connected ? buttonStyle : disabledButtonStyle}
              title="Shutdown machine"
            >
              ⏻ Shutdown
            </button>
          </>
        )}

        {/* Server Info */}
        {state.serverName && (
          <div style={{ marginLeft: 'auto', fontSize: '0.8rem', color: '#666' }}>
            {state.serverName}
          </div>
        )}
      </div>

      {/* Clipboard Dialog */}
      {showClipboardDialog && (
        <div
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: 1000,
          }}
          onClick={() => setShowClipboardDialog(false)}
        >
          <div
            style={{
              backgroundColor: 'white',
              padding: '1.5rem',
              borderRadius: '8px',
              minWidth: '400px',
              maxWidth: '600px',
            }}
            onClick={(e) => e.stopPropagation()}
          >
            <h3 style={{ margin: '0 0 1rem 0' }}>Paste to Remote Desktop</h3>
            <textarea
              value={clipboardText}
              onChange={(e) => setClipboardText(e.target.value)}
              placeholder="Enter text to paste to the remote desktop..."
              style={{
                width: '100%',
                height: '120px',
                padding: '0.5rem',
                border: '1px solid #ccc',
                borderRadius: '4px',
                resize: 'vertical',
                fontFamily: 'monospace',
              }}
              autoFocus
            />
            <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
              <button
                onClick={() => setShowClipboardDialog(false)}
                style={{
                  padding: '0.5rem 1rem',
                  border: '1px solid #ccc',
                  backgroundColor: 'white',
                  borderRadius: '4px',
                  cursor: 'pointer',
                }}
              >
                Cancel
              </button>
              <button
                onClick={handleClipboardPaste}
                disabled={!clipboardText.trim()}
                style={{
                  padding: '0.5rem 1rem',
                  border: 'none',
                  backgroundColor: clipboardText.trim() ? '#3498db' : '#ccc',
                  color: 'white',
                  borderRadius: '4px',
                  cursor: clipboardText.trim() ? 'pointer' : 'not-allowed',
                }}
              >
                Paste
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
