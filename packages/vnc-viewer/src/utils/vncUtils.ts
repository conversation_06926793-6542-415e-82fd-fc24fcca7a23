import type { VNCConnectionConfig } from '../types/index.js';

/**
 * Build WebSocket URL for VNC connection
 */
export function buildVNCUrl(config: VNCConnectionConfig): string {
  const protocol = config.encrypt ? 'wss:' : 'ws:';
  const path = config.path || 'websockify';
  
  return `${protocol}//${config.host}:${config.port}/${path}`;
}

/**
 * Validate VNC connection configuration
 */
export function validateVNCConfig(config: VNCConnectionConfig): string[] {
  const errors: string[] = [];

  if (!config.host || config.host.trim() === '') {
    errors.push('Host is required');
  }

  if (!config.port || config.port < 1 || config.port > 65535) {
    errors.push('Port must be between 1 and 65535');
  }

  return errors;
}

/**
 * Generate random connection ID
 */
export function generateConnectionId(): string {
  return Math.random().toString(36).substring(2, 15) + 
         Math.random().toString(36).substring(2, 15);
}

/**
 * Format VNC error messages
 */
export function formatVNCError(error: any): string {
  if (typeof error === 'string') {
    return error;
  }

  if (error?.message) {
    return error.message;
  }

  if (error?.detail) {
    return error.detail;
  }

  return 'Unknown VNC error occurred';
}

/**
 * Check if browser supports required features
 */
export function checkBrowserSupport(): { supported: boolean; missing: string[] } {
  const missing: string[] = [];

  if (typeof WebSocket === 'undefined') {
    missing.push('WebSocket');
  }

  if (typeof ArrayBuffer === 'undefined') {
    missing.push('ArrayBuffer');
  }

  if (typeof Uint8Array === 'undefined') {
    missing.push('Uint8Array');
  }

  if (!document.createElement('canvas').getContext) {
    missing.push('Canvas');
  }

  return {
    supported: missing.length === 0,
    missing
  };
}

/**
 * Calculate optimal canvas size based on container and screen dimensions
 */
export function calculateCanvasSize(
  containerWidth: number,
  containerHeight: number,
  screenWidth: number,
  screenHeight: number,
  scaleViewport: boolean = false
): { width: number; height: number; scale: number } {
  if (!scaleViewport) {
    return {
      width: screenWidth,
      height: screenHeight,
      scale: 1
    };
  }

  const scaleX = containerWidth / screenWidth;
  const scaleY = containerHeight / screenHeight;
  const scale = Math.min(scaleX, scaleY, 1); // Don't scale up

  return {
    width: Math.floor(screenWidth * scale),
    height: Math.floor(screenHeight * scale),
    scale
  };
}

/**
 * Convert key event to VNC keysym
 */
export function keyEventToKeysym(event: KeyboardEvent): number {
  // Basic ASCII characters
  if (event.key.length === 1) {
    const code = event.key.charCodeAt(0);
    if (code >= 32 && code <= 126) {
      return code;
    }
  }

  // Special keys mapping
  const specialKeys: Record<string, number> = {
    'Backspace': 0xff08,
    'Tab': 0xff09,
    'Enter': 0xff0d,
    'Escape': 0xff1b,
    'Delete': 0xffff,
    'Home': 0xff50,
    'End': 0xff57,
    'PageUp': 0xff55,
    'PageDown': 0xff56,
    'ArrowLeft': 0xff51,
    'ArrowUp': 0xff52,
    'ArrowRight': 0xff53,
    'ArrowDown': 0xff54,
    'F1': 0xffbe,
    'F2': 0xffbf,
    'F3': 0xffc0,
    'F4': 0xffc1,
    'F5': 0xffc2,
    'F6': 0xffc3,
    'F7': 0xffc4,
    'F8': 0xffc5,
    'F9': 0xffc6,
    'F10': 0xffc7,
    'F11': 0xffc8,
    'F12': 0xffc9,
    'Shift': 0xffe1,
    'Control': 0xffe3,
    'Alt': 0xffe9,
    'Meta': 0xffe7,
  };

  return specialKeys[event.key] || 0;
}

/**
 * Debounce function for performance optimization
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

/**
 * Throttle function for performance optimization
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean;
  
  return (...args: Parameters<T>) => {
    if (!inThrottle) {
      func(...args);
      inThrottle = true;
      setTimeout(() => inThrottle = false, limit);
    }
  };
}
