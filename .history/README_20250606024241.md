# Container Runner

A containerized Node.js application runner with desktop environments, browser integration, and remote access capabilities.

## Features

- **Containerized Environment**: Lightweight Linux containers with Node.js pre-installed
- **Desktop Integration**: XFCE/LXDE desktop environments with Chrome browser
- **Application Deployment**: Upload files or clone from Git repositories
- **Remote Access**: VNC and noVNC web interface for desktop interaction
- **Real-time Monitoring**: WebSocket-based real-time updates and logs
- **REST API**: Complete API for container lifecycle management

## Architecture

```
┌─────────────────────────────────────┐
│ Next.js Frontend (Port 3000)       │
├─────────────────────────────────────┤
│ Express.js API (Port 3001)         │
├─────────────────────────────────────┤
│ Docker Containers                  │
│ ├─ Node.js + Desktop + Browser     │
│ ├─ VNC Server (5900-5999)         │
│ ├─ noVNC Web (6080-6179)          │
│ └─ App Server (3000-3999)         │
└─────────────────────────────────────┘
```

## Quick Start

### Prerequisites

- Node.js 20+
- Docker
- pnpm

### 1. Install Dependencies

```bash
pnpm install
```

### 2. Build Docker Images

```bash
cd apps/api/docker
chmod +x build-images.sh
./build-images.sh
```

### 3. Start the API Server

```bash
cd apps/api
pnpm dev
```

### 4. Start the Frontend

```bash
cd apps/web
pnpm dev
```

### 5. Access the Application

- Frontend: http://localhost:3000
- API: http://localhost:3001
- Dashboard: http://localhost:3000/dashboard
