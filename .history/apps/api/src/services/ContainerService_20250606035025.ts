import Docker from 'dockerode';
import { v4 as uuidv4 } from 'uuid';
import { config } from '@/config/index.js';
import { Logger } from '@/utils/logger.js';
import { portManager } from '@/utils/portManager.js';
import type { Container, CreateContainerRequest, ContainerStats, VNCConnection } from '@/types/index.js';

export class ContainerService {
  private docker: Docker;
  private containers = new Map<string, Container>();

  constructor() {
    this.docker = new Docker({ socketPath: config.docker.socketPath });
    this.initializeNetwork();
    this.syncExistingContainers();
  }

  private async initializeNetwork(): Promise<void> {
    try {
      const networks = await this.docker.listNetworks();
      const networkExists = networks.some((net: any) => net.Name === config.docker.network);

      if (!networkExists) {
        await this.docker.createNetwork({
          Name: config.docker.network,
          Driver: 'bridge',
        });
        Logger.info(`Created Docker network: ${config.docker.network}`);
      }
    } catch (error) {
      Logger.error('Failed to initialize Docker network', { error });
    }
  }

  private async syncExistingContainers(): Promise<void> {
    try {
      const dockerContainers = await this.docker.listContainers({ all: true });
      const ourContainers = dockerContainers.filter((container: any) =>
        container.Names.some((name: string) => name.startsWith('/container-runner-'))
      );

      for (const dockerContainer of ourContainers) {
        const containerName = dockerContainer.Names[0].replace('/container-runner-', '');
        const containerId = containerName;

        // Extract port mappings
        const ports = this.extractPortMappings(dockerContainer.Ports);

        const container: Container = {
          id: containerId,
          name: dockerContainer.Labels?.['container.name'] || `container-${containerId.substring(0, 8)}`,
          status: dockerContainer.State === 'running' ? 'running' : 'stopped',
          nodeVersion: dockerContainer.Labels?.['container.nodeVersion'] || '20',
          desktopEnvironment: dockerContainer.Labels?.['container.desktop'] || 'xfce',
          createdAt: new Date(dockerContainer.Created * 1000),
          updatedAt: new Date(),
          ports,
        };

        this.containers.set(containerId, container);
        Logger.info(`Synced existing container: ${containerId}`);
      }
    } catch (error) {
      Logger.error('Failed to sync existing containers', { error });
    }
  }

  private extractPortMappings(dockerPorts: any[]): { vnc: number; noVNC: number; app: number } {
    const ports = { vnc: 5901, noVNC: 6081, app: 3000 };

    for (const port of dockerPorts) {
      if (port.PrivatePort === 5901 && port.PublicPort) {
        ports.vnc = port.PublicPort;
      } else if (port.PrivatePort === 6081 && port.PublicPort) {
        ports.noVNC = port.PublicPort;
      } else if (port.PrivatePort === 3000 && port.PublicPort) {
        ports.app = port.PublicPort;
      }
    }

    return ports;
  }

  async createContainer(request: CreateContainerRequest): Promise<Container> {
    if (this.containers.size >= config.container.maxContainers) {
      throw new Error('Maximum number of containers reached');
    }

    const containerId = uuidv4();
    const vncPort = portManager.allocateVNCPort();
    const noVNCPort = portManager.allocateNoVNCPort();
    const appPort = portManager.allocateAppPort();

    const container: Container = {
      id: containerId,
      name: request.name,
      status: 'creating',
      nodeVersion: request.nodeVersion || config.container.defaultNodeVersion,
      desktopEnvironment: request.desktopEnvironment || config.container.defaultDesktop,
      createdAt: new Date(),
      updatedAt: new Date(),
      ports: {
        vnc: vncPort,
        noVNC: noVNCPort,
        app: appPort,
      },
    };

    this.containers.set(containerId, container);

    try {
      await this.startDockerContainer(container);
      container.status = 'running';
      container.updatedAt = new Date();
      
      Logger.info(`Container created successfully`, { containerId, name: request.name });
      return container;
    } catch (error) {
      container.status = 'error';
      container.updatedAt = new Date();
      
      // Release allocated ports on failure
      portManager.releasePorts([vncPort, noVNCPort, appPort]);
      
      Logger.error('Failed to create container', { containerId, error });
      throw error;
    }
  }

  private async startDockerContainer(container: Container): Promise<void> {
    const imageName = `${config.docker.baseImage}:latest`;
    Logger.info(`Creating container with image: ${imageName}`);

    const dockerContainer = await this.docker.createContainer({
      Image: imageName,
      name: `container-runner-${container.id}`,
      Env: [
        `VNC_PORT=${container.ports.vnc}`,
        `NOVNC_PORT=${container.ports.noVNC}`,
        `APP_PORT=${container.ports.app}`,
        `NODE_VERSION=${container.nodeVersion}`,
        `DESKTOP=${container.desktopEnvironment}`,
      ],
      ExposedPorts: {
        '5901/tcp': {},
        '6081/tcp': {},
        '3000/tcp': {},
      },
      User: 'root',
      HostConfig: {
        PortBindings: {
          '5901/tcp': [{ HostPort: container.ports.vnc.toString() }],
          '6081/tcp': [{ HostPort: container.ports.noVNC.toString() }],
          '3000/tcp': [{ HostPort: container.ports.app.toString() }],
        },
        NetworkMode: 'bridge',
        Memory: 1024 * 1024 * 1024, // 1GB
        CpuShares: 512,
      },
      WorkingDir: '/workspace',
    });

    await dockerContainer.start();
  }

  async getContainer(id: string): Promise<Container | null> {
    return this.containers.get(id) || null;
  }

  async listContainers(): Promise<Container[]> {
    return Array.from(this.containers.values());
  }

  async deleteContainer(id: string): Promise<void> {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    try {
      const dockerContainer = this.docker.getContainer(`container-runner-${id}`);
      await dockerContainer.stop();
      await dockerContainer.remove();
      
      // Release ports
      portManager.releasePorts([
        container.ports.vnc,
        container.ports.noVNC,
        container.ports.app,
      ]);
      
      this.containers.delete(id);
      Logger.info(`Container deleted successfully`, { containerId: id });
    } catch (error) {
      Logger.error('Failed to delete container', { containerId: id, error });
      throw error;
    }
  }

  async getContainerStats(id: string): Promise<ContainerStats> {
    const dockerContainer = this.docker.getContainer(`container-runner-${id}`);
    const stats = await dockerContainer.stats({ stream: false });
    
    return {
      cpu: this.calculateCpuPercent(stats),
      memory: stats.memory_stats.usage || 0,
      network: {
        rx: stats.networks?.eth0?.rx_bytes || 0,
        tx: stats.networks?.eth0?.tx_bytes || 0,
      },
    };
  }

  private calculateCpuPercent(stats: any): number {
    const cpuDelta = stats.cpu_stats.cpu_usage.total_usage - stats.precpu_stats.cpu_usage.total_usage;
    const systemDelta = stats.cpu_stats.system_cpu_usage - stats.precpu_stats.system_cpu_usage;
    const numberCpus = stats.cpu_stats.online_cpus || 1;
    
    if (systemDelta > 0 && cpuDelta > 0) {
      return (cpuDelta / systemDelta) * numberCpus * 100;
    }
    return 0;
  }

  getVNCConnection(id: string): VNCConnection {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    return {
      host: config.host,
      port: container.ports.vnc,
      webUrl: `http://${config.host}:${container.ports.noVNC}`,
    };
  }

  async testDockerConnection(): Promise<string[]> {
    try {
      const images = await this.docker.listImages();
      return images.map((img: any) => img.RepoTags?.[0] || 'unknown').filter(Boolean);
    } catch (error) {
      Logger.error('Docker connection test failed', { error });
      throw error;
    }
  }
}
