export const config = {
  port: process.env.PORT || 3002,
  host: process.env.HOST || '0.0.0.0',
  
  // Docker configuration
  docker: {
    socketPath: process.env.DOCKER_SOCKET || '/var/run/docker.sock',
    baseImage: 'node-desktop-runner',
    network: 'container-runner-network',
  },
  
  // Port ranges for containers
  ports: {
    vnc: {
      start: 5900,
      end: 5999,
    },
    noVNC: {
      start: 6080,
      end: 6179,
    },
    app: {
      start: 3000,
      end: 3999,
    },
  },
  
  // File upload configuration
  upload: {
    maxSize: 100 * 1024 * 1024, // 100MB
    allowedExtensions: ['.js', '.ts', '.json', '.md', '.txt', '.html', '.css'],
    tempDir: './uploads',
  },
  
  // Container limits
  container: {
    maxContainers: 10,
    defaultNodeVersion: '20',
    defaultDesktop: 'xfce' as const,
    timeout: 300000, // 5 minutes
  },
  
  // Security
  security: {
    corsOrigins: process.env.CORS_ORIGINS?.split(',') || ['http://localhost:3000'],
    rateLimitWindow: 15 * 60 * 1000, // 15 minutes
    rateLimitMax: 100, // requests per window
  },
} as const;
