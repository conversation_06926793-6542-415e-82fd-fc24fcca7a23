"use client"

import { useState, useRef } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { But<PERSON> } from "@workspace/ui/components/button"
import { Input } from "@workspace/ui/components/input"
import { Label } from "@workspace/ui/components/label"
import { VNCViewer, VNCToolbar, type VNCViewerRef } from "@workspace/vnc-viewer"
import { Monitor, Settings } from "lucide-react"

export default function VNCDemoPage() {
  const [vncUrl, setVncUrl] = useState("ws://localhost:6081")
  const [isConnected, setIsConnected] = useState(false)
  const [connectionStatus, setConnectionStatus] = useState("Disconnected")
  const vncRef = useRef<VNCViewerRef>(null)

  const handleConnect = () => {
    vncRef.current?.connect()
  }

  const handleDisconnect = () => {
    vncRef.current?.disconnect()
  }

  const handleVNCConnect = () => {
    setIsConnected(true)
    setConnectionStatus("Connected")
  }

  const handleVNCDisconnect = (clean: boolean) => {
    setIsConnected(false)
    setConnectionStatus(clean ? "Disconnected" : "Connection Lost")
  }

  const handleCredentialsRequired = (types: string[]) => {
    console.log("Credentials required:", types)
    setConnectionStatus("Credentials Required")
  }

  const handleSecurityFailure = (retryCount: number, types: string[]) => {
    setConnectionStatus(`Security Failure (${retryCount})`)
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
            VNC Viewer Demo
          </h1>
          <p className="text-gray-600 dark:text-gray-300">
            Test the integrated VNC viewer component
          </p>
        </div>

        {/* Connection Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Connection Settings
            </CardTitle>
            <CardDescription>
              Configure VNC connection parameters
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="vncUrl">VNC WebSocket URL</Label>
                <Input
                  id="vncUrl"
                  value={vncUrl}
                  onChange={(e) => setVncUrl(e.target.value)}
                  placeholder="ws://localhost:6081"
                />
              </div>
              <div className="space-y-2">
                <Label>Status</Label>
                <div className="flex items-center gap-2 p-2 border rounded">
                  <div 
                    className={`w-2 h-2 rounded-full ${
                      isConnected ? 'bg-green-500' : 'bg-red-500'
                    }`} 
                  />
                  <span className="text-sm">{connectionStatus}</span>
                </div>
              </div>
              <div className="space-y-2">
                <Label>Actions</Label>
                <div className="flex gap-2">
                  <Button 
                    onClick={handleConnect} 
                    disabled={isConnected}
                    size="sm"
                  >
                    Connect
                  </Button>
                  <Button 
                    onClick={handleDisconnect} 
                    disabled={!isConnected}
                    variant="outline"
                    size="sm"
                  >
                    Disconnect
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* VNC Viewer */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              VNC Desktop
            </CardTitle>
            <CardDescription>
              Remote desktop viewer with full interaction support
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            {/* VNC Toolbar */}
            <VNCToolbar
              vncRef={vncRef}
              state={{
                connected: isConnected,
                connecting: false,
                disconnected: !isConnected,
                error: null,
                rfbVersion: null,
                serverName: null,
              }}
              className="border-b"
            />

            {/* VNC Viewer */}
            <div className="h-96 bg-gray-900">
              <VNCViewer
                ref={vncRef}
                url={vncUrl}
                autoConnect={false}
                viewOnly={false}
                scaleViewport={true}
                dragViewport={true}
                focusOnClick={true}
                showDotCursor={true}
                background="#2c3e50"
                onConnect={handleVNCConnect}
                onDisconnect={handleVNCDisconnect}
                onCredentialsRequired={handleCredentialsRequired}
                onSecurityFailure={handleSecurityFailure}
                onDesktopName={(name) => {
                  console.log("Desktop name:", name)
                }}
                className="w-full h-full"
              />
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Instructions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <h3 className="font-semibold mb-2">Testing with Container Runner</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li>Create a container in the dashboard</li>
                  <li>Wait for it to start running</li>
                  <li>Get the noVNC port from container details</li>
                  <li>Update the URL above to ws://localhost:[noVNC-port]</li>
                  <li>Click Connect to view the desktop</li>
                </ol>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Testing with External VNC</h3>
                <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600 dark:text-gray-300">
                  <li>Install a VNC server (like TightVNC, RealVNC)</li>
                  <li>Set up websockify proxy: websockify 6081 localhost:5901</li>
                  <li>Use URL: ws://localhost:6081</li>
                  <li>Click Connect to test the viewer</li>
                </ol>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                Features Demonstrated
              </h4>
              <ul className="list-disc list-inside space-y-1 text-sm text-blue-800 dark:text-blue-200">
                <li>Real-time VNC connection over WebSocket</li>
                <li>Interactive desktop with mouse and keyboard support</li>
                <li>Toolbar with common VNC actions (Ctrl+Alt+Del, clipboard, etc.)</li>
                <li>Automatic scaling and viewport management</li>
                <li>Connection status monitoring and error handling</li>
                <li>Fullscreen mode support</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
