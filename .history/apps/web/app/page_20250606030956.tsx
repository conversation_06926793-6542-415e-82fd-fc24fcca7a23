import Link from "next/link"
import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Container, Play, Code, Monitor } from "lucide-react"

export default function Page() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-16">
        <div className="text-center mb-16">
          <h1 className="text-5xl font-bold text-gray-900 dark:text-white mb-6">
            Container Runner
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Deploy and run Node.js applications in isolated containers with desktop environments,
            browser integration, and remote access capabilities.
          </p>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          <Card className="text-center">
            <CardHeader>
              <Container className="w-12 h-12 mx-auto text-blue-600 mb-4" />
              <CardTitle>Containerized Environment</CardTitle>
              <CardDescription>
                Lightweight Linux containers with Node.js pre-installed and configured
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Monitor className="w-12 h-12 mx-auto text-green-600 mb-4" />
              <CardTitle>Desktop Integration</CardTitle>
              <CardDescription>
                XFCE/LXDE desktop environments with Chrome browser for application preview
              </CardDescription>
            </CardHeader>
          </Card>

          <Card className="text-center">
            <CardHeader>
              <Play className="w-12 h-12 mx-auto text-purple-600 mb-4" />
              <CardTitle>Instant Deployment</CardTitle>
              <CardDescription>
                Upload code or clone from Git, build, and run with automatic browser preview
              </CardDescription>
            </CardHeader>
          </Card>
        </div>

        <div className="text-center space-y-4">
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/dashboard">
              <Button size="lg" className="text-lg px-8 py-4">
                <Code className="w-5 h-5 mr-2" />
                Open Dashboard
              </Button>
            </Link>
            <Link href="/vnc-demo">
              <Button size="lg" variant="outline" className="text-lg px-8 py-4">
                <Monitor className="w-5 h-5 mr-2" />
                VNC Demo
              </Button>
            </Link>
          </div>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Try the VNC viewer demo to test desktop integration
          </p>
        </div>
      </div>
    </div>
  )
}
