import axios from 'axios';

export interface Container {
  id: string;
  name: string;
  status: 'creating' | 'running' | 'stopped' | 'error';
  nodeVersion: string;
  desktopEnvironment: 'xfce' | 'lxde';
  createdAt: string;
  updatedAt: string;
  ports: {
    vnc: number;
    noVNC: number;
    app: number;
  };
  application?: {
    name: string;
    status: 'deploying' | 'running' | 'stopped' | 'error';
    port: number;
    buildLogs: string[];
    runtimeLogs: string[];
  };
}

export interface CreateContainerRequest {
  name: string;
  nodeVersion?: string;
  desktopEnvironment?: 'xfce' | 'lxde';
}

export interface DeployAppRequest {
  type: 'upload' | 'git';
  gitUrl?: string;
  gitBranch?: string;
  buildCommand?: string;
  startCommand?: string;
  port?: number;
}

export interface ContainerStats {
  cpu: number;
  memory: number;
  network: {
    rx: number;
    tx: number;
  };
}

export interface VNCConnection {
  host: string;
  port: number;
  password?: string;
  webUrl: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  timeout: 30000,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    console.error('API Error:', error.response?.data || error.message);
    return Promise.reject(error.response?.data || error);
  }
);

export const containerApi = {
  // List all containers
  list: (): Promise<ApiResponse<Container[]>> => 
    api.get('/containers'),

  // Create new container
  create: (data: CreateContainerRequest): Promise<ApiResponse<Container>> =>
    api.post('/containers', data),

  // Get container details
  get: (id: string): Promise<ApiResponse<Container>> =>
    api.get(`/containers/${id}`),

  // Delete container
  delete: (id: string): Promise<ApiResponse> =>
    api.delete(`/containers/${id}`),

  // Deploy application
  deploy: (id: string, data: DeployAppRequest, files?: File[]): Promise<ApiResponse<Container>> => {
    const formData = new FormData();
    
    // Add form fields
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined) {
        formData.append(key, value.toString());
      }
    });

    // Add files if provided
    if (files) {
      files.forEach((file) => {
        formData.append('files', file);
      });
    }

    return api.post(`/containers/${id}/deploy`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Start application
  start: (id: string, startCommand?: string): Promise<ApiResponse<Container>> =>
    api.post(`/containers/${id}/start`, { startCommand }),

  // Stop application
  stop: (id: string): Promise<ApiResponse<Container>> =>
    api.post(`/containers/${id}/stop`),

  // Get container statistics
  getStats: (id: string): Promise<ApiResponse<ContainerStats>> =>
    api.get(`/containers/${id}/stats`),

  // Get VNC connection info
  getVNC: (id: string): Promise<ApiResponse<VNCConnection>> =>
    api.get(`/containers/${id}/vnc`),
};

export default api;
