"use client"

import { useState } from "react"
import { useQuery } from "@tanstack/react-query"
import { Plus, RefreshCw } from "lucide-react"

import { But<PERSON> } from "@workspace/ui/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { containerApi } from "@/lib/api"
import { useWebSocket } from "@/hooks/useWebSocket"

import { ContainerList } from "./components/ContainerList"
import { CreateContainerDialog } from "./components/CreateContainerDialog"
import { ContainerDetails } from "./components/ContainerDetails"

export default function DashboardPage() {
  const [selectedContainerId, setSelectedContainerId] = useState<string | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)

  // Fetch containers
  const {
    data: containersResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['containers'],
    queryFn: containerApi.list,
    refetchInterval: 5000, // Refetch every 5 seconds
  })

  // WebSocket connection for real-time updates
  const { isConnected, lastMessage } = useWebSocket({
    onMessage: (message) => {
      console.log('WebSocket message:', message)
      // Refetch containers when we receive updates
      if (message.type === 'container_status') {
        refetch()
      }
    },
  })

  const containers = containersResponse?.data || []
  const selectedContainer = containers.find(c => c.id === selectedContainerId)

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              Container Dashboard
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mt-2">
              Manage your Node.js application containers
            </p>
          </div>
          
          <div className="flex items-center gap-4">
            {/* Connection status */}
            <div className="flex items-center gap-2">
              <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
              <span className="text-sm text-gray-600 dark:text-gray-300">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => refetch()}
              disabled={isLoading}
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>

            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="w-4 h-4 mr-2" />
              New Container
            </Button>
          </div>
        </div>

        {/* Error state */}
        {error && (
          <Card className="mb-6 border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-900/20">
            <CardHeader>
              <CardTitle className="text-red-800 dark:text-red-200">Error</CardTitle>
              <CardDescription className="text-red-600 dark:text-red-300">
                Failed to load containers. Please check your API connection.
              </CardDescription>
            </CardHeader>
          </Card>
        )}

        <div className="grid lg:grid-cols-3 gap-6">
          {/* Container List */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle>Containers ({containers.length})</CardTitle>
                <CardDescription>
                  Click on a container to view details
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0">
                <ContainerList
                  containers={containers}
                  selectedId={selectedContainerId}
                  onSelect={setSelectedContainerId}
                  isLoading={isLoading}
                />
              </CardContent>
            </Card>
          </div>

          {/* Container Details */}
          <div className="lg:col-span-2">
            {selectedContainer ? (
              <ContainerDetails
                container={selectedContainer}
                onUpdate={() => refetch()}
              />
            ) : (
              <Card className="h-96 flex items-center justify-center">
                <CardContent className="text-center">
                  <div className="text-gray-400 dark:text-gray-600 mb-4">
                    <Plus className="w-16 h-16 mx-auto mb-4" />
                  </div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                    No Container Selected
                  </h3>
                  <p className="text-gray-600 dark:text-gray-300 mb-4">
                    Select a container from the list to view details and manage applications
                  </p>
                  <Button onClick={() => setIsCreateDialogOpen(true)}>
                    Create Your First Container
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Create Container Dialog */}
        <CreateContainerDialog
          open={isCreateDialogOpen}
          onOpenChange={setIsCreateDialogOpen}
          onSuccess={() => {
            refetch()
            setIsCreateDialogOpen(false)
          }}
        />
      </div>
    </div>
  )
}
