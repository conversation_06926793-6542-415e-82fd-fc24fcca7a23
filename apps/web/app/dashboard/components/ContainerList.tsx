"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, AlertCircle, Clock } from "lucide-react"
import { Badge } from "@workspace/ui/components/badge"
import { Button } from "@workspace/ui/components/button"
import { Skeleton } from "@workspace/ui/components/skeleton"
import type { Container as ContainerType } from "@/lib/api"

interface ContainerListProps {
  containers: ContainerType[]
  selectedId: string | null
  onSelect: (id: string) => void
  isLoading: boolean
}

const getStatusIcon = (status: ContainerType['status']) => {
  switch (status) {
    case 'running':
      return <Play className="w-4 h-4 text-green-600" />
    case 'stopped':
      return <Square className="w-4 h-4 text-gray-600" />
    case 'creating':
      return <Clock className="w-4 h-4 text-blue-600" />
    case 'error':
      return <AlertCircle className="w-4 h-4 text-red-600" />
    default:
      return <Container className="w-4 h-4 text-gray-600" />
  }
}

const getStatusBadge = (status: ContainerType['status']) => {
  switch (status) {
    case 'running':
      return <Badge variant="default" className="bg-green-100 text-green-800">Running</Badge>
    case 'stopped':
      return <Badge variant="secondary">Stopped</Badge>
    case 'creating':
      return <Badge variant="default" className="bg-blue-100 text-blue-800">Creating</Badge>
    case 'error':
      return <Badge variant="destructive">Error</Badge>
    default:
      return <Badge variant="outline">{status}</Badge>
  }
}

const getAppStatusBadge = (status?: string) => {
  if (!status) return null
  
  switch (status) {
    case 'running':
      return <Badge variant="default" className="bg-green-100 text-green-800 text-xs">App Running</Badge>
    case 'deploying':
      return <Badge variant="default" className="bg-blue-100 text-blue-800 text-xs">Deploying</Badge>
    case 'stopped':
      return <Badge variant="secondary" className="text-xs">App Stopped</Badge>
    case 'error':
      return <Badge variant="destructive" className="text-xs">App Error</Badge>
    default:
      return <Badge variant="outline" className="text-xs">{status}</Badge>
  }
}

export function ContainerList({ containers, selectedId, onSelect, isLoading }: ContainerListProps) {
  if (isLoading) {
    return (
      <div className="space-y-2 p-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-3 w-1/2" />
          </div>
        ))}
      </div>
    )
  }

  if (containers.length === 0) {
    return (
      <div className="p-8 text-center">
        <Container className="w-12 h-12 mx-auto text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
          No Containers
        </h3>
        <p className="text-gray-600 dark:text-gray-300 text-sm">
          Create your first container to get started
        </p>
      </div>
    )
  }

  return (
    <div className="divide-y divide-gray-200 dark:divide-gray-700">
      {containers.map((container) => (
        <div
          key={container.id}
          className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800 ${
            selectedId === container.id ? 'bg-blue-50 dark:bg-blue-900/20 border-r-2 border-blue-500' : ''
          }`}
          onClick={() => onSelect(container.id)}
        >
          <div className="flex items-start justify-between">
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-2">
                {getStatusIcon(container.status)}
                <h3 className="text-sm font-medium text-gray-900 dark:text-white truncate">
                  {container.name}
                </h3>
              </div>
              
              <div className="flex items-center gap-2 mb-2">
                {getStatusBadge(container.status)}
                {getAppStatusBadge(container.application?.status)}
              </div>

              <div className="text-xs text-gray-500 dark:text-gray-400 space-y-1">
                <div>Node.js {container.nodeVersion}</div>
                <div className="capitalize">{container.desktopEnvironment}</div>
                <div>Created {new Date(container.createdAt).toLocaleDateString()}</div>
              </div>

              {container.application && (
                <div className="mt-2 text-xs text-gray-600 dark:text-gray-300">
                  <div>App: {container.application.name}</div>
                  <div>Port: {container.application.port}</div>
                </div>
              )}
            </div>
          </div>
        </div>
      ))}
    </div>
  )
}
