"use client"

import { useState, useRef, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { Monitor, Maximize2, Minimize2, Refresh<PERSON>w, AlertCircle } from "lucide-react"

import { But<PERSON> } from "@workspace/ui/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Alert, AlertDescription } from "@workspace/ui/components/alert"
import { VNCViewer, VNCToolbar, type VNCViewerRef } from "@workspace/vnc-viewer"

import { containerApi, type Container } from "@/lib/api"

interface VNCDesktopViewerProps {
  container: Container
}

export function VNCDesktopViewer({ container }: VNCDesktopViewerProps) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  const vncRef = useRef<VNCViewerRef>(null)

  // Fetch VNC connection info
  const { data: vncData, isLoading, error, refetch } = useQuery({
    queryKey: ['vnc', container.id],
    queryFn: () => containerApi.getVNC(container.id),
    enabled: container.status === 'running',
    refetchInterval: 30000, // Refetch every 30 seconds
  })

  // Build VNC URL
  const vncUrl = vncData?.data ? 
    `ws://${vncData.data.host}:${vncData.data.port}` : 
    null

  const handleConnect = () => {
    vncRef.current?.connect()
  }

  const handleDisconnect = () => {
    vncRef.current?.disconnect()
  }

  const handleRefresh = () => {
    refetch()
    setConnectionError(null)
  }

  const handleVNCConnect = () => {
    setConnectionError(null)
  }

  const handleVNCDisconnect = (clean: boolean) => {
    if (!clean) {
      setConnectionError('Connection lost unexpectedly')
    }
  }

  const handleVNCError = (error: string) => {
    setConnectionError(error)
  }

  if (container.status !== 'running') {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            Desktop Viewer
          </CardTitle>
          <CardDescription>
            VNC desktop access for the container
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Container must be running to access the desktop
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    )
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            Desktop Viewer
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Loading VNC connection...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error || !vncUrl) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Monitor className="w-5 h-5" />
            Desktop Viewer
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Failed to get VNC connection information
            </AlertDescription>
          </Alert>
          <Button onClick={handleRefresh} className="mt-4" variant="outline">
            <RefreshCw className="w-4 h-4 mr-2" />
            Retry
          </Button>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={isExpanded ? "fixed inset-4 z-50 flex flex-col" : ""}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5" />
              Desktop Viewer
            </CardTitle>
            <CardDescription>
              VNC connection to {vncData?.data?.host}:{vncData?.data?.port}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <Minimize2 className="w-4 h-4" />
              ) : (
                <Maximize2 className="w-4 h-4" />
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className={isExpanded ? "flex-1 flex flex-col p-0" : "p-0"}>
        {connectionError && (
          <Alert variant="destructive" className="m-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{connectionError}</AlertDescription>
          </Alert>
        )}

        {/* VNC Toolbar */}
        <VNCToolbar
          vncRef={vncRef}
          state={vncRef.current?.getState() || {
            connected: false,
            connecting: false,
            disconnected: true,
            error: null,
            rfbVersion: null,
            serverName: null,
          }}
          showFullscreen={!isExpanded}
          className="border-b"
        />

        {/* VNC Viewer */}
        <div className={isExpanded ? "flex-1" : "h-96"}>
          <VNCViewer
            ref={vncRef}
            url={vncUrl}
            autoConnect={true}
            viewOnly={false}
            scaleViewport={!isExpanded}
            dragViewport={true}
            focusOnClick={true}
            showDotCursor={true}
            background="#2c3e50"
            onConnect={handleVNCConnect}
            onDisconnect={handleVNCDisconnect}
            onCredentialsRequired={(types) => {
              console.log('VNC credentials required:', types)
            }}
            onSecurityFailure={(retryCount, types) => {
              setConnectionError(`Security failure (attempt ${retryCount}): ${types.join(', ')}`)
            }}
            onDesktopName={(name) => {
              console.log('VNC desktop name:', name)
            }}
            className="w-full h-full"
          />
        </div>
      </CardContent>

      {/* Overlay close button for expanded mode */}
      {isExpanded && (
        <Button
          variant="secondary"
          size="sm"
          onClick={() => setIsExpanded(false)}
          className="absolute top-4 right-4 z-10"
        >
          <Minimize2 className="w-4 h-4 mr-2" />
          Exit Fullscreen
        </Button>
      )}
    </Card>
  )
}
