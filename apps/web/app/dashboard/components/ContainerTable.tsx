"use client"

import { useState } from "react"
import { 
  Container, 
  <PERSON>, 
  Square, 
  Trash2, 
  Monitor, 
  MoreHorizontal,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2
} from "lucide-react"
import { useMutation } from "@tanstack/react-query"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Badge } from "@workspace/ui/components/badge"
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@workspace/ui/components/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@workspace/ui/components/dropdown-menu"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Skeleton } from "@workspace/ui/components/skeleton"

import { containerApi, type Container as ContainerType } from "@/lib/api"

interface ContainerTableProps {
  containers: ContainerType[]
  selectedId: string | null
  onSelect: (id: string) => void
  onUpdate: () => void
  isLoading: boolean
}

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'running':
      return <CheckCircle className="w-4 h-4 text-green-500" />
    case 'stopped':
      return <Square className="w-4 h-4 text-gray-500" />
    case 'creating':
      return <Loader2 className="w-4 h-4 text-blue-500 animate-spin" />
    case 'error':
      return <AlertCircle className="w-4 h-4 text-red-500" />
    default:
      return <Clock className="w-4 h-4 text-yellow-500" />
  }
}

const getStatusColor = (status: string) => {
  switch (status) {
    case 'running':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
    case 'stopped':
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    case 'creating':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
    case 'error':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
    default:
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

export function ContainerTable({ 
  containers, 
  selectedId, 
  onSelect, 
  onUpdate, 
  isLoading 
}: ContainerTableProps) {
  const [deletingId, setDeletingId] = useState<string | null>(null)

  const deleteMutation = useMutation({
    mutationFn: async (id: string) => {
      setDeletingId(id)
      return containerApi.delete(id)
    },
    onSuccess: () => {
      toast.success("Container deleted successfully")
      onUpdate()
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to delete container")
    },
    onSettled: () => {
      setDeletingId(null)
    }
  })

  const handleDelete = (container: ContainerType) => {
    if (confirm(`Are you sure you want to delete "${container.name}"? This action cannot be undone.`)) {
      deleteMutation.mutate(container.id)
    }
  }

  const handleOpenDesktop = (container: ContainerType) => {
    // This would typically fetch VNC info and open in new window
    toast.info("Opening desktop viewer...")
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Containers</CardTitle>
          <CardDescription>Loading containers...</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (containers.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Container className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            No Containers
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Create your first container to get started
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Containers ({containers.length})</CardTitle>
        <CardDescription>
          Manage your containerized applications
        </CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Name</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Node.js</TableHead>
              <TableHead>Desktop</TableHead>
              <TableHead>Created</TableHead>
              <TableHead>Application</TableHead>
              <TableHead className="w-[50px]"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {containers.map((container) => (
              <TableRow 
                key={container.id}
                className={`cursor-pointer hover:bg-muted/50 ${
                  selectedId === container.id ? 'bg-muted' : ''
                }`}
                onClick={() => onSelect(container.id)}
              >
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(container.status)}
                    {container.name}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant="secondary" 
                    className={getStatusColor(container.status)}
                  >
                    {container.status}
                  </Badge>
                </TableCell>
                <TableCell>{container.nodeVersion}</TableCell>
                <TableCell className="capitalize">
                  {container.desktopEnvironment}
                </TableCell>
                <TableCell className="text-muted-foreground">
                  {formatDate(container.createdAt)}
                </TableCell>
                <TableCell>
                  {container.application ? (
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant="outline"
                        className={getStatusColor(container.application.status)}
                      >
                        {container.application.status}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {container.application.name}
                      </span>
                    </div>
                  ) : (
                    <span className="text-sm text-muted-foreground">None</span>
                  )}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={(e) => e.stopPropagation()}
                      >
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      {container.status === 'running' && (
                        <>
                          <DropdownMenuItem onClick={() => handleOpenDesktop(container)}>
                            <Monitor className="w-4 h-4 mr-2" />
                            Open Desktop
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                        </>
                      )}
                      <DropdownMenuItem 
                        onClick={() => handleDelete(container)}
                        className="text-destructive"
                        disabled={deletingId === container.id}
                      >
                        {deletingId === container.id ? (
                          <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        ) : (
                          <Trash2 className="w-4 h-4 mr-2" />
                        )}
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
}
