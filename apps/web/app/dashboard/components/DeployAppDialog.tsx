"use client"

import { useState, useCallback } from "react"
import { useMutation } from "@tanstack/react-query"
import { useDropzone } from "react-dropzone"
import { Upload, GitBranch, Loader2, File, X, Settings, Play } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogHeader, 
  DialogTitle 
} from "@workspace/ui/components/dialog"
import { Input } from "@workspace/ui/components/input"
import { Label } from "@workspace/ui/components/label"
import { Textarea } from "@workspace/ui/components/textarea"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@workspace/ui/components/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Badge } from "@workspace/ui/components/badge"
import { Separator } from "@workspace/ui/components/separator"
import { Progress } from "@workspace/ui/components/progress"

import { containerApi, type DeployAppRequest } from "@/lib/api"

interface DeployAppDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  containerId: string
  onSuccess: () => void
}

interface UploadedFile {
  file: File
  preview: string
}

export function DeployAppDialog({ 
  open, 
  onOpenChange, 
  containerId, 
  onSuccess 
}: DeployAppDialogProps) {
  const [deployType, setDeployType] = useState<'upload' | 'git'>('upload')
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([])
  const [gitFormData, setGitFormData] = useState({
    gitUrl: '',
    gitBranch: 'main',
    buildCommand: 'npm install',
    startCommand: 'npm start',
    port: 3000
  })
  const [uploadProgress, setUploadProgress] = useState(0)

  const deployMutation = useMutation({
    mutationFn: async (data: DeployAppRequest) => {
      if (deployType === 'upload' && uploadedFiles.length > 0) {
        // Simulate file upload progress
        const formData = new FormData()
        uploadedFiles.forEach((uploadedFile) => {
          formData.append('files', uploadedFile.file)
        })
        formData.append('buildCommand', data.buildCommand || 'npm install')
        formData.append('startCommand', data.startCommand || 'npm start')
        formData.append('port', String(data.port || 3000))

        // Simulate upload progress
        for (let i = 0; i <= 100; i += 10) {
          setUploadProgress(i)
          await new Promise(resolve => setTimeout(resolve, 100))
        }

        return containerApi.deploy(containerId, {
          type: 'upload',
          buildCommand: data.buildCommand,
          startCommand: data.startCommand,
          port: data.port
        })
      } else {
        return containerApi.deployApp(containerId, data)
      }
    },
    onSuccess: () => {
      toast.success("Application deployed successfully!")
      onSuccess()
      handleClose()
    },
    onError: (error: any) => {
      toast.error(error.message || "Failed to deploy application")
    }
  })

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles = acceptedFiles.map(file => ({
      file,
      preview: URL.createObjectURL(file)
    }))
    setUploadedFiles(prev => [...prev, ...newFiles])
  }, [])

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/zip': ['.zip'],
      'application/x-tar': ['.tar'],
      'application/gzip': ['.tar.gz'],
      'text/javascript': ['.js'],
      'application/json': ['.json'],
      'text/plain': ['.txt', '.md']
    },
    maxSize: 100 * 1024 * 1024 // 100MB
  })

  const removeFile = (index: number) => {
    setUploadedFiles(prev => {
      const newFiles = [...prev]
      URL.revokeObjectURL(newFiles[index].preview)
      newFiles.splice(index, 1)
      return newFiles
    })
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    if (deployType === 'upload' && uploadedFiles.length === 0) {
      toast.error("Please upload at least one file")
      return
    }
    
    if (deployType === 'git' && !gitFormData.gitUrl.trim()) {
      toast.error("Please enter a Git repository URL")
      return
    }

    const deployData: DeployAppRequest = {
      type: deployType,
      ...(deployType === 'git' && {
        gitUrl: gitFormData.gitUrl,
        gitBranch: gitFormData.gitBranch,
      }),
      buildCommand: deployType === 'git' ? gitFormData.buildCommand : 'npm install',
      startCommand: deployType === 'git' ? gitFormData.startCommand : 'npm start',
      port: deployType === 'git' ? gitFormData.port : 3000
    }

    deployMutation.mutate(deployData)
  }

  const handleClose = () => {
    if (!deployMutation.isPending) {
      onOpenChange(false)
      setUploadedFiles([])
      setGitFormData({
        gitUrl: '',
        gitBranch: 'main',
        buildCommand: 'npm install',
        startCommand: 'npm start',
        port: 3000
      })
      setUploadProgress(0)
      deployMutation.reset()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Deploy Application
          </DialogTitle>
          <DialogDescription>
            Deploy a Node.js application to your container. You can upload files or clone from a Git repository.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <Tabs value={deployType} onValueChange={(value) => setDeployType(value as 'upload' | 'git')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="upload" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Upload Files
              </TabsTrigger>
              <TabsTrigger value="git" className="flex items-center gap-2">
                <GitBranch className="w-4 h-4" />
                Git Repository
              </TabsTrigger>
            </TabsList>

            <TabsContent value="upload" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Upload Files</CardTitle>
                  <CardDescription>
                    Upload your application files as a ZIP archive or individual files
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                      isDragActive 
                        ? 'border-primary bg-primary/5' 
                        : 'border-muted-foreground/25 hover:border-muted-foreground/50'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <Upload className="w-12 h-12 mx-auto mb-4 text-muted-foreground" />
                    {isDragActive ? (
                      <p className="text-lg font-medium">Drop files here...</p>
                    ) : (
                      <div>
                        <p className="text-lg font-medium mb-2">
                          Drag & drop files here, or click to select
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Supports ZIP, TAR, and individual files (max 100MB)
                        </p>
                      </div>
                    )}
                  </div>

                  {uploadedFiles.length > 0 && (
                    <div className="mt-4 space-y-2">
                      <Label>Uploaded Files ({uploadedFiles.length})</Label>
                      <div className="space-y-2 max-h-32 overflow-y-auto">
                        {uploadedFiles.map((uploadedFile, index) => (
                          <div key={index} className="flex items-center justify-between p-2 bg-muted rounded-md">
                            <div className="flex items-center gap-2">
                              <File className="w-4 h-4" />
                              <span className="text-sm truncate">{uploadedFile.file.name}</span>
                              <Badge variant="secondary" className="text-xs">
                                {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
                              </Badge>
                            </div>
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              onClick={() => removeFile(index)}
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="git" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Git Repository</CardTitle>
                  <CardDescription>
                    Clone and deploy from a Git repository
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="col-span-2">
                      <Label htmlFor="gitUrl">Repository URL *</Label>
                      <Input
                        id="gitUrl"
                        placeholder="https://github.com/username/repo.git"
                        value={gitFormData.gitUrl}
                        onChange={(e) => setGitFormData(prev => ({ ...prev, gitUrl: e.target.value }))}
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="gitBranch">Branch</Label>
                      <Input
                        id="gitBranch"
                        placeholder="main"
                        value={gitFormData.gitBranch}
                        onChange={(e) => setGitFormData(prev => ({ ...prev, gitBranch: e.target.value }))}
                      />
                    </div>
                    <div>
                      <Label htmlFor="port">Port</Label>
                      <Input
                        id="port"
                        type="number"
                        placeholder="3000"
                        value={gitFormData.port}
                        onChange={(e) => setGitFormData(prev => ({ ...prev, port: parseInt(e.target.value) || 3000 }))}
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Settings className="w-5 h-5" />
                    Build Configuration
                  </CardTitle>
                  <CardDescription>
                    Configure how your application should be built and started
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="buildCommand">Build Command</Label>
                    <Input
                      id="buildCommand"
                      placeholder="npm install"
                      value={gitFormData.buildCommand}
                      onChange={(e) => setGitFormData(prev => ({ ...prev, buildCommand: e.target.value }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="startCommand">Start Command</Label>
                    <Input
                      id="startCommand"
                      placeholder="npm start"
                      value={gitFormData.startCommand}
                      onChange={(e) => setGitFormData(prev => ({ ...prev, startCommand: e.target.value }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {deployMutation.isPending && uploadProgress > 0 && (
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Uploading files...</span>
                    <span>{uploadProgress}%</span>
                  </div>
                  <Progress value={uploadProgress} />
                </div>
              </CardContent>
            </Card>
          )}

          <Separator />

          <div className="flex justify-end gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={deployMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={deployMutation.isPending}
            >
              {deployMutation.isPending ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Deploying...
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-2" />
                  Deploy Application
                </>
              )}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
