"use client"

import { useState, useEffect } from "react"
import { useQuery } from "@tanstack/react-query"
import { 
  Activity, 
  HardDrive, 
  Network, 
  Cpu, 
  MemoryStick,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Progress } from "@workspace/ui/components/progress"
import { Badge } from "@workspace/ui/components/badge"
import { 
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  type ChartConfig
} from "@workspace/ui/components/chart"
import { LineChart, Line, XAxis, YAxis, ResponsiveContainer, AreaChart, Area } from "recharts"

import { containerApi, type ContainerStats } from "@/lib/api"

interface ContainerStatsProps {
  containerId: string
  isRunning: boolean
}

interface HistoricalData {
  timestamp: string
  cpu: number
  memory: number
  networkRx: number
  networkTx: number
}

const chartConfig = {
  cpu: {
    label: "CPU %",
    color: "hsl(var(--chart-1))",
  },
  memory: {
    label: "Memory MB",
    color: "hsl(var(--chart-2))",
  },
  networkRx: {
    label: "Network RX KB",
    color: "hsl(var(--chart-3))",
  },
  networkTx: {
    label: "Network TX KB",
    color: "hsl(var(--chart-4))",
  },
} satisfies ChartConfig

export function ContainerStats({ containerId, isRunning }: ContainerStatsProps) {
  const [historicalData, setHistoricalData] = useState<HistoricalData[]>([])
  const [previousStats, setPreviousStats] = useState<ContainerStats | null>(null)

  // Fetch current stats
  const { data: statsData, isLoading } = useQuery({
    queryKey: ['stats', containerId],
    queryFn: () => containerApi.getStats(containerId),
    enabled: isRunning,
    refetchInterval: 5000, // Update every 5 seconds
  })

  // Update historical data when new stats arrive
  useEffect(() => {
    if (statsData?.data) {
      const newDataPoint: HistoricalData = {
        timestamp: new Date().toLocaleTimeString(),
        cpu: statsData.data.cpu,
        memory: statsData.data.memory / 1024 / 1024, // Convert to MB
        networkRx: statsData.data.network.rx / 1024, // Convert to KB
        networkTx: statsData.data.network.tx / 1024, // Convert to KB
      }

      setHistoricalData(prev => {
        const updated = [...prev, newDataPoint]
        // Keep only last 20 data points
        return updated.slice(-20)
      })

      setPreviousStats(statsData.data)
    }
  }, [statsData])

  const getTrend = (current: number, previous: number) => {
    if (!previous) return null
    const diff = current - previous
    if (Math.abs(diff) < 0.1) return 'stable'
    return diff > 0 ? 'up' : 'down'
  }

  const getTrendIcon = (trend: string | null) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="w-4 h-4 text-red-500" />
      case 'down':
        return <TrendingDown className="w-4 h-4 text-green-500" />
      case 'stable':
        return <Minus className="w-4 h-4 text-gray-500" />
      default:
        return null
    }
  }

  const formatBytes = (bytes: number) => {
    const sizes = ['B', 'KB', 'MB', 'GB']
    if (bytes === 0) return '0 B'
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  if (!isRunning) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <Activity className="w-16 h-16 mx-auto mb-4 text-gray-400" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Container Not Running
          </h3>
          <p className="text-gray-600 dark:text-gray-300">
            Statistics are only available when the container is running
          </p>
        </CardContent>
      </Card>
    )
  }

  if (isLoading || !statsData?.data) {
    return (
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="animate-pulse">
                <div className="h-8 bg-gray-200 rounded mb-2"></div>
                <div className="h-2 bg-gray-200 rounded"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  const stats = statsData.data
  const cpuTrend = previousStats ? getTrend(stats.cpu, previousStats.cpu) : null
  const memoryTrend = previousStats ? getTrend(stats.memory, previousStats.memory) : null

  return (
    <div className="space-y-6">
      {/* Current Stats Cards */}
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">CPU Usage</CardTitle>
            <div className="flex items-center gap-1">
              {getTrendIcon(cpuTrend)}
              <Cpu className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.cpu.toFixed(1)}%</div>
            <Progress value={stats.cpu} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {stats.cpu > 80 ? 'High usage' : stats.cpu > 50 ? 'Moderate usage' : 'Low usage'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Memory Usage</CardTitle>
            <div className="flex items-center gap-1">
              {getTrendIcon(memoryTrend)}
              <MemoryStick className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.memory / 1024 / 1024).toFixed(0)} MB
            </div>
            <Progress value={(stats.memory / 1024 / 1024 / 1024) * 100} className="mt-2" />
            <p className="text-xs text-muted-foreground mt-1">
              {formatBytes(stats.memory)}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network RX</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.network.rx / 1024).toFixed(0)} KB
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Received data
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Network TX</CardTitle>
            <Network className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {(stats.network.tx / 1024).toFixed(0)} KB
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Transmitted data
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Historical Charts */}
      {historicalData.length > 1 && (
        <div className="grid md:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">CPU & Memory Usage</CardTitle>
              <CardDescription>
                Real-time performance metrics over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={historicalData}>
                    <XAxis 
                      dataKey="timestamp" 
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Line 
                      type="monotone" 
                      dataKey="cpu" 
                      stroke="var(--color-cpu)" 
                      strokeWidth={2}
                      dot={false}
                    />
                    <Line 
                      type="monotone" 
                      dataKey="memory" 
                      stroke="var(--color-memory)" 
                      strokeWidth={2}
                      dot={false}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Network Activity</CardTitle>
              <CardDescription>
                Data transfer rates over time
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ChartContainer config={chartConfig} className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <AreaChart data={historicalData}>
                    <XAxis 
                      dataKey="timestamp" 
                      tick={{ fontSize: 12 }}
                      interval="preserveStartEnd"
                    />
                    <YAxis tick={{ fontSize: 12 }} />
                    <ChartTooltip content={<ChartTooltipContent />} />
                    <Area 
                      type="monotone" 
                      dataKey="networkRx" 
                      stackId="1"
                      stroke="var(--color-networkRx)" 
                      fill="var(--color-networkRx)"
                      fillOpacity={0.6}
                    />
                    <Area 
                      type="monotone" 
                      dataKey="networkTx" 
                      stackId="1"
                      stroke="var(--color-networkTx)" 
                      fill="var(--color-networkTx)"
                      fillOpacity={0.6}
                    />
                  </AreaChart>
                </ResponsiveContainer>
              </ChartContainer>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
