"use client"

import { useState } from "react"
import { useMutation } from "@tanstack/react-query"
import { Loader2 } from "lucide-react"

import { But<PERSON> } from "@workspace/ui/components/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog"
import { Input } from "@workspace/ui/components/input"
import { Label } from "@workspace/ui/components/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@workspace/ui/components/select"
import { containerApi, type CreateContainerRequest } from "@/lib/api"

interface CreateContainerDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onSuccess: () => void
}

export function CreateContainerDialog({ open, onOpenChange, onSuccess }: CreateContainerDialogProps) {
  const [formData, setFormData] = useState<CreateContainerRequest>({
    name: '',
    nodeVersion: '20',
    desktopEnvironment: 'xfce',
  })

  const createMutation = useMutation({
    mutationFn: containerApi.create,
    onSuccess: () => {
      onSuccess()
      setFormData({ name: '', nodeVersion: '20', desktopEnvironment: 'xfce' })
    },
  })

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.name.trim()) return
    
    createMutation.mutate(formData)
  }

  const handleClose = () => {
    if (!createMutation.isPending) {
      onOpenChange(false)
      setFormData({ name: '', nodeVersion: '20', desktopEnvironment: 'xfce' })
      createMutation.reset()
    }
  }

  return (
    <Dialog open={open} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Container</DialogTitle>
          <DialogDescription>
            Create a new containerized environment for your Node.js applications.
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Container Name</Label>
            <Input
              id="name"
              placeholder="my-awesome-app"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              disabled={createMutation.isPending}
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="nodeVersion">Node.js Version</Label>
            <Select
              value={formData.nodeVersion}
              onValueChange={(value) => setFormData(prev => ({ ...prev, nodeVersion: value }))}
              disabled={createMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select Node.js version" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="18">Node.js 18 LTS</SelectItem>
                <SelectItem value="20">Node.js 20 LTS</SelectItem>
                <SelectItem value="22">Node.js 22</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="desktop">Desktop Environment</Label>
            <Select
              value={formData.desktopEnvironment}
              onValueChange={(value: 'xfce' | 'lxde') => setFormData(prev => ({ ...prev, desktopEnvironment: value }))}
              disabled={createMutation.isPending}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select desktop environment" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="xfce">XFCE (Recommended)</SelectItem>
                <SelectItem value="lxde">LXDE (Lightweight)</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {createMutation.error && (
            <div className="text-sm text-red-600 dark:text-red-400">
              {createMutation.error instanceof Error 
                ? createMutation.error.message 
                : 'Failed to create container'}
            </div>
          )}

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={createMutation.isPending}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={createMutation.isPending || !formData.name.trim()}
            >
              {createMutation.isPending && (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              )}
              Create Container
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
