"use client"

import { useState, useEffect, useRef } from "react"
import { 
  Terminal, 
  Download, 
  Trash2, 
  Maximize2, 
  Minimize2,
  <PERSON><PERSON>,
  Refresh<PERSON><PERSON>,
  Search
} from "lucide-react"

import { But<PERSON> } from "@workspace/ui/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Input } from "@workspace/ui/components/input"
import { Badge } from "@workspace/ui/components/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@workspace/ui/components/tabs"
import { ScrollArea } from "@workspace/ui/components/scroll-area"
import { Separator } from "@workspace/ui/components/separator"
import { toast } from "sonner"

interface TerminalViewerProps {
  buildLogs: string[]
  runtimeLogs: string[]
  isExpanded?: boolean
  onToggleExpanded?: () => void
}

export function TerminalViewer({ 
  buildLogs, 
  runtimeLogs, 
  isExpanded = false,
  onToggleExpanded 
}: TerminalViewerProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [activeTab, setActiveTab] = useState("runtime")
  const buildScrollRef = useRef<HTMLDivElement>(null)
  const runtimeScrollRef = useRef<HTMLDivElement>(null)

  // Auto-scroll to bottom when new logs arrive
  useEffect(() => {
    if (activeTab === "build" && buildScrollRef.current) {
      buildScrollRef.current.scrollTop = buildScrollRef.current.scrollHeight
    }
  }, [buildLogs, activeTab])

  useEffect(() => {
    if (activeTab === "runtime" && runtimeScrollRef.current) {
      runtimeScrollRef.current.scrollTop = runtimeScrollRef.current.scrollHeight
    }
  }, [runtimeLogs, activeTab])

  const filterLogs = (logs: string[]) => {
    if (!searchTerm) return logs
    return logs.filter(log => 
      log.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  const copyLogs = (logs: string[]) => {
    const logText = logs.join('\n')
    navigator.clipboard.writeText(logText)
    toast.success("Logs copied to clipboard")
  }

  const downloadLogs = (logs: string[], filename: string) => {
    const logText = logs.join('\n')
    const blob = new Blob([logText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = filename
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success("Logs downloaded")
  }

  const clearLogs = () => {
    // This would typically call an API to clear logs
    toast.info("Clear logs functionality would be implemented here")
  }

  const formatLogLine = (log: string, index: number) => {
    // Basic log formatting - could be enhanced with syntax highlighting
    const timestamp = new Date().toLocaleTimeString()
    const isError = log.toLowerCase().includes('error') || log.toLowerCase().includes('failed')
    const isWarning = log.toLowerCase().includes('warn') || log.toLowerCase().includes('warning')
    const isSuccess = log.toLowerCase().includes('success') || log.toLowerCase().includes('completed')

    let className = "text-green-400"
    if (isError) className = "text-red-400"
    else if (isWarning) className = "text-yellow-400"
    else if (isSuccess) className = "text-blue-400"

    return (
      <div key={index} className="flex gap-2 text-sm font-mono">
        <span className="text-gray-500 text-xs w-20 flex-shrink-0">
          {String(index + 1).padStart(3, '0')}
        </span>
        <span className={className}>{log}</span>
      </div>
    )
  }

  const LogContent = ({ logs, scrollRef, type }: { 
    logs: string[], 
    scrollRef: React.RefObject<HTMLDivElement>,
    type: string 
  }) => {
    const filteredLogs = filterLogs(logs)
    
    return (
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary">
              {filteredLogs.length} lines
            </Badge>
            {searchTerm && (
              <Badge variant="outline">
                Filtered from {logs.length} total
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => copyLogs(filteredLogs)}
            >
              <Copy className="w-4 h-4 mr-2" />
              Copy
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => downloadLogs(filteredLogs, `${type}-logs.txt`)}
            >
              <Download className="w-4 h-4 mr-2" />
              Download
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={clearLogs}
            >
              <Trash2 className="w-4 h-4 mr-2" />
              Clear
            </Button>
          </div>
        </div>

        <ScrollArea 
          className={`bg-gray-900 rounded-lg p-4 ${isExpanded ? 'h-96' : 'h-64'}`}
          ref={scrollRef}
        >
          {filteredLogs.length > 0 ? (
            <div className="space-y-1">
              {filteredLogs.map((log, index) => formatLogLine(log, index))}
            </div>
          ) : (
            <div className="text-gray-500 text-center py-8">
              {searchTerm ? 'No logs match your search' : `No ${type} logs available`}
            </div>
          )}
        </ScrollArea>
      </div>
    )
  }

  return (
    <Card className={isExpanded ? "fixed inset-4 z-50 flex flex-col" : ""}>
      <CardHeader className="flex-shrink-0">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Terminal className="w-5 h-5" />
              Application Logs
            </CardTitle>
            <CardDescription>
              View build and runtime logs for your application
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
              <Search className="w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-48"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                // Refresh logs
                toast.info("Refreshing logs...")
              }}
            >
              <RefreshCw className="w-4 h-4" />
            </Button>
            {onToggleExpanded && (
              <Button
                variant="outline"
                size="sm"
                onClick={onToggleExpanded}
              >
                {isExpanded ? (
                  <Minimize2 className="w-4 h-4" />
                ) : (
                  <Maximize2 className="w-4 h-4" />
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className={isExpanded ? "flex-1 overflow-hidden" : ""}>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="runtime" className="flex items-center gap-2">
              Runtime Logs
              <Badge variant="secondary" className="text-xs">
                {runtimeLogs.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger value="build" className="flex items-center gap-2">
              Build Logs
              <Badge variant="secondary" className="text-xs">
                {buildLogs.length}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="runtime" className="mt-4">
            <LogContent 
              logs={runtimeLogs} 
              scrollRef={runtimeScrollRef}
              type="runtime"
            />
          </TabsContent>

          <TabsContent value="build" className="mt-4">
            <LogContent 
              logs={buildLogs} 
              scrollRef={buildScrollRef}
              type="build"
            />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
