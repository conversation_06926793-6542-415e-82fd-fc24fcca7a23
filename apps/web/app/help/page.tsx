"use client"

import { 
  HelpCircle, 
  Book, 
  MessageCircle, 
  ExternalLink, 
  Container,
  Monitor,
  Upload,
  <PERSON>tings,
  Zap
} from "lucide-react"

import { <PERSON><PERSON> } from "@workspace/ui/components/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@workspace/ui/components/card"
import { Badge } from "@workspace/ui/components/badge"
import { Separator } from "@workspace/ui/components/separator"
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger
} from "@workspace/ui/components/accordion"

export default function HelpPage() {
  const quickStartSteps = [
    {
      icon: Container,
      title: "Create a Container",
      description: "Start by creating a new container with your preferred Node.js version and desktop environment."
    },
    {
      icon: Upload,
      title: "Deploy Your App",
      description: "Upload your application files or clone from a Git repository to deploy your Node.js application."
    },
    {
      icon: Monitor,
      title: "Access Desktop",
      description: "Use the VNC viewer to access the desktop environment and interact with your application."
    },
    {
      icon: Settings,
      title: "Monitor & Manage",
      description: "View logs, monitor performance, and manage your containers through the dashboard."
    }
  ]

  const faqs = [
    {
      question: "What is VibeKraft?",
      answer: "VibeKraft is a containerized Node.js application runner that provides isolated environments with desktop integration, browser access, and remote VNC capabilities for developing and testing applications."
    },
    {
      question: "How do I create a new container?",
      answer: "Click the 'Create Container' button in the dashboard, choose your Node.js version and desktop environment (XFCE or LXDE), give it a name, and click create. The container will be ready in a few moments."
    },
    {
      question: "Can I deploy applications from Git repositories?",
      answer: "Yes! When deploying an application, you can choose to clone from a Git repository. Just provide the repository URL, branch name, and build/start commands."
    },
    {
      question: "How do I access the desktop environment?",
      answer: "Once your container is running, go to the Desktop tab in the container details view. You can use the built-in VNC viewer or open the desktop in a new browser window."
    },
    {
      question: "What Node.js versions are supported?",
      answer: "We support Node.js 18 LTS, 20 LTS, and 22 Current. You can select your preferred version when creating a container."
    },
    {
      question: "How do I view application logs?",
      answer: "Navigate to the Logs tab in your container details. You can view both build logs and runtime logs, search through them, and download them for offline analysis."
    },
    {
      question: "Can I monitor container performance?",
      answer: "Yes! The Statistics tab provides real-time monitoring of CPU usage, memory consumption, and network activity with historical charts."
    },
    {
      question: "Is my data secure?",
      answer: "Each container runs in an isolated environment. Your applications and data are contained within their respective containers and don't interfere with each other."
    }
  ]

  const resources = [
    {
      title: "API Documentation",
      description: "Complete API reference for integrating with VibeKraft",
      icon: Book,
      href: "/api/docs",
      badge: "API"
    },
    {
      title: "GitHub Repository",
      description: "Source code, issues, and contributions",
      icon: ExternalLink,
      href: "https://github.com/vibekraft/vibekraft",
      badge: "GitHub"
    },
    {
      title: "Community Support",
      description: "Get help from the community",
      icon: MessageCircle,
      href: "https://discord.gg/vibekraft",
      badge: "Discord"
    }
  ]

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Help & Support
          </h1>
          <p className="text-gray-600 dark:text-gray-300 mt-2">
            Get help with VibeKraft and learn how to make the most of your containerized applications
          </p>
        </div>
      </div>

      {/* Quick Start Guide */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="w-5 h-5" />
            Quick Start Guide
          </CardTitle>
          <CardDescription>
            Get up and running with VibeKraft in 4 simple steps
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickStartSteps.map((step, index) => (
              <div key={index} className="text-center space-y-3">
                <div className="flex items-center justify-center w-12 h-12 mx-auto bg-primary/10 rounded-lg">
                  <step.icon className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    {index + 1}. {step.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <HelpCircle className="w-5 h-5" />
            Frequently Asked Questions
          </CardTitle>
          <CardDescription>
            Common questions and answers about using VibeKraft
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index}`}>
                <AccordionTrigger className="text-left">
                  {faq.question}
                </AccordionTrigger>
                <AccordionContent className="text-gray-600 dark:text-gray-300">
                  {faq.answer}
                </AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </CardContent>
      </Card>

      {/* Resources */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Book className="w-5 h-5" />
            Additional Resources
          </CardTitle>
          <CardDescription>
            Documentation, community, and support resources
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid md:grid-cols-3 gap-4">
            {resources.map((resource, index) => (
              <Card key={index} className="hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <resource.icon className="w-5 h-5 text-primary" />
                    <Badge variant="secondary" className="text-xs">
                      {resource.badge}
                    </Badge>
                  </div>
                  <h3 className="font-medium text-gray-900 dark:text-white mb-2">
                    {resource.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">
                    {resource.description}
                  </p>
                  <Button variant="outline" size="sm" className="w-full" asChild>
                    <a href={resource.href} target="_blank" rel="noopener noreferrer">
                      <ExternalLink className="w-4 h-4 mr-2" />
                      Visit
                    </a>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Contact Support */}
      <Card>
        <CardHeader>
          <CardTitle>Need More Help?</CardTitle>
          <CardDescription>
            Can't find what you're looking for? Get in touch with our support team
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            <Button className="flex-1" asChild>
              <a href="mailto:<EMAIL>">
                <MessageCircle className="w-4 h-4 mr-2" />
                Contact Support
              </a>
            </Button>
            <Button variant="outline" className="flex-1" asChild>
              <a href="https://github.com/vibekraft/vibekraft/issues" target="_blank" rel="noopener noreferrer">
                <ExternalLink className="w-4 h-4 mr-2" />
                Report an Issue
              </a>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
