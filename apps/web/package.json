{"name": "web", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@tanstack/react-query": "^5.62.7", "@webcontainer/api": "^1.6.1", "@workspace/ui": "workspace:*", "@workspace/vnc-viewer": "workspace:*", "ai": "^4.3.16", "axios": "^1.7.9", "lucide-react": "^0.475.0", "next": "^15.2.3", "next-themes": "^0.4.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.5", "recharts": "^2.15.3", "socket.io-client": "^4.8.1", "sonner": "^2.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@workspace/eslint-config": "workspace:^", "@workspace/typescript-config": "workspace:*", "typescript": "^5.7.3"}}