/** @type {import('next').NextConfig} */
const nextConfig = {
  transpilePackages: ["@workspace/ui", "@workspace/vnc-viewer"],
  webpack: (config, { isServer }) => {
    // Handle noVNC modules
    config.module.rules.push({
      test: /\.js$/,
      include: /node_modules\/@novnc/,
      type: 'javascript/esm',
    });

    // Enable top-level await
    config.experiments = {
      ...config.experiments,
      topLevelAwait: true,
    };

    return config;
  },
}

export default nextConfig
