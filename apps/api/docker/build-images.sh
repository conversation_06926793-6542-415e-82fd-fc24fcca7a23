#!/bin/bash

# Build script for container images
set -e

echo "Building Node.js Desktop Container Images..."

# Node.js versions to build
NODE_VERSIONS=("18" "20" "22")

# Desktop environments to build
DESKTOPS=("xfce" "lxde")

# Build images for each combination
for node_version in "${NODE_VERSIONS[@]}"; do
    for desktop in "${DESKTOPS[@]}"; do
        echo "Building node-desktop-runner:${node_version}-${desktop}..."
        
        docker build \
            --build-arg NODE_VERSION=${node_version} \
            --build-arg DESKTOP=${desktop} \
            -t node-desktop-runner:${node_version}-${desktop} \
            -f node-desktop/Dockerfile \
            node-desktop/
        
        echo "✅ Built node-desktop-runner:${node_version}-${desktop}"
    done
done

# Create latest tags for Node 20 + XFCE (default)
docker tag node-desktop-runner:20-xfce node-desktop-runner:latest
docker tag node-desktop-runner:20-xfce node-desktop-runner:20
docker tag node-desktop-runner:20-xfce node-desktop-runner:xfce

echo "🎉 All images built successfully!"
echo ""
echo "Available images:"
docker images | grep node-desktop-runner
