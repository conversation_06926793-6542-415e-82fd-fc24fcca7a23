[supervisord]
nodaemon=true
user=root
logfile=/var/log/supervisor/supervisord.log
pidfile=/var/run/supervisord.pid

[program:xvfb]
command=/usr/bin/Xvfb :1 -screen 0 1920x1080x24 -ac +extension GLX +render -noreset
user=developer
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/xvfb.log
stderr_logfile=/var/log/supervisor/xvfb.log

[program:x11vnc]
command=/usr/bin/x11vnc -display :1 -nopw -listen localhost -xkb -ncache 10 -ncache_cr -forever -shared
user=developer
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/x11vnc.log
stderr_logfile=/var/log/supervisor/x11vnc.log
depends_on=xvfb

[program:desktop]
command=/usr/local/bin/start-desktop.sh
user=developer
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/desktop.log
stderr_logfile=/var/log/supervisor/desktop.log
depends_on=xvfb
environment=DISPLAY=":1"

[program:novnc]
command=/opt/noVNC/utils/novnc_proxy --vnc localhost:5901 --listen %(ENV_NOVNC_PORT)s
user=developer
autostart=true
autorestart=true
stdout_logfile=/var/log/supervisor/novnc.log
stderr_logfile=/var/log/supervisor/novnc.log
depends_on=x11vnc
