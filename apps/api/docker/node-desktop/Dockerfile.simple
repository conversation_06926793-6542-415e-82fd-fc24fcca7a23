# Simple Node.js Desktop Container for Testing
FROM ubuntu:22.04

# Avoid prompts from apt
ENV DEBIAN_FRONTEND=noninteractive

# Install basic dependencies
RUN apt-get update && apt-get install -y \
    curl \
    wget \
    gnupg \
    lsb-release \
    software-properties-common \
    ca-certificates \
    apt-transport-https \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js 20
RUN curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && node --version \
    && npm --version

# Install basic desktop dependencies
RUN apt-get update && apt-get install -y \
    xorg \
    xserver-xorg-video-dummy \
    x11vnc \
    xvfb \
    supervisor \
    && rm -rf /var/lib/apt/lists/*

# Install XFCE desktop
RUN apt-get update && apt-get install -y \
    xfce4 \
    xfce4-terminal \
    && rm -rf /var/lib/apt/lists/*

# Install Firefox (lighter than Chrome for testing)
RUN apt-get update && apt-get install -y \
    firefox \
    && rm -rf /var/lib/apt/lists/*

# Install noVNC
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    git \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /opt
RUN git clone https://github.com/novnc/noVNC.git \
    && pip3 install websockify

# Create user
RUN useradd -m -s /bin/bash developer \
    && usermod -aG sudo developer \
    && echo "developer:developer" | chpasswd

# Set up workspace
WORKDIR /workspace
RUN chown developer:developer /workspace

# Copy configuration files
COPY supervisord.conf /etc/supervisor/conf.d/supervisord.conf
COPY start-desktop.sh /usr/local/bin/start-desktop.sh
COPY xorg.conf /etc/X11/xorg.conf

# Make scripts executable
RUN chmod +x /usr/local/bin/start-desktop.sh

# Environment variables
ENV DISPLAY=:1
ENV VNC_PORT=5901
ENV NOVNC_PORT=6081
ENV APP_PORT=3000

# Expose ports
EXPOSE 5901 6081 3000

# Switch to developer user
USER developer

# Start supervisor
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]
