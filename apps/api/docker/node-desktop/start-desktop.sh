#!/bin/bash

# Wait for X server to be ready
while ! xdpyinfo -display :1 >/dev/null 2>&1; do
    echo "Waiting for X server..."
    sleep 1
done

echo "X server is ready, starting desktop environment..."

# Start desktop environment based on DESKTOP variable
case "$DESKTOP" in
    "xfce")
        echo "Starting XFCE desktop..."
        startxfce4 &
        ;;
    "lxde")
        echo "Starting LXDE desktop..."
        startlxde &
        ;;
    *)
        echo "Unknown desktop environment: $DESKTOP, defaulting to XFCE"
        startxfce4 &
        ;;
esac

# Wait a bit for desktop to start
sleep 5

# Set up desktop background and basic settings
if command -v xfconf-query >/dev/null 2>&1; then
    # XFCE settings
    xfconf-query -c xfce4-desktop -p /backdrop/screen0/monitor0/workspace0/last-image -s ""
    xfconf-query -c xfce4-desktop -p /backdrop/screen0/monitor0/workspace0/color-style -s 0
    xfconf-query -c xfce4-desktop -p /backdrop/screen0/monitor0/workspace0/rgba1 -t double -t double -t double -t double -s 0.2 -s 0.2 -s 0.2 -s 1.0
fi

# Keep the script running
tail -f /dev/null
