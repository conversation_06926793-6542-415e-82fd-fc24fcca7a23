{"name": "api", "version": "0.0.1", "type": "module", "private": true, "scripts": {"dev": "tsx watch src/server.ts", "build": "tsc", "start": "node dist/server.js", "lint": "eslint . --max-warnings 0", "typecheck": "tsc --noEmit"}, "dependencies": {"express": "^4.19.2", "cors": "^2.8.5", "multer": "^1.4.5-lts.1", "ws": "^8.18.0", "dockerode": "^4.0.2", "simple-git": "^3.25.0", "archiver": "^7.0.1", "uuid": "^10.0.0", "joi": "^17.13.3", "helmet": "^8.0.0", "compression": "^1.7.4", "morgan": "^1.10.0"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.12", "@types/ws": "^8.5.12", "@types/uuid": "^10.0.0", "@types/compression": "^1.7.5", "@types/morgan": "^1.9.9", "@types/archiver": "^6.0.2", "@types/node": "^20", "@workspace/eslint-config": "workspace:*", "@workspace/typescript-config": "workspace:*", "tsx": "^4.19.2", "typescript": "^5.7.3"}}