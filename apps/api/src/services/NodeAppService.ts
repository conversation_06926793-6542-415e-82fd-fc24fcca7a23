import Docker from 'dockerode';
import { simpleGit } from 'simple-git';
import archiver from 'archiver';
import fs from 'fs/promises';
import path from 'path';
import { config } from '@/config/index.js';
import { Logger } from '@/utils/logger.js';
import type { Container, DeployAppRequest } from '@/types/index.js';

export class NodeAppService {
  private docker: Docker;

  constructor() {
    this.docker = new Docker({ socketPath: config.docker.socketPath });
  }

  async deployApplication(
    container: Container,
    request: DeployAppRequest,
    files?: Express.Multer.File[]
  ): Promise<void> {
    if (!container.application) {
      container.application = {
        name: 'app',
        status: 'deploying',
        port: request.port || 3000,
        buildLogs: [],
        runtimeLogs: [],
      };
    }

    container.application.status = 'deploying';
    container.application.buildLogs = [];
    container.updatedAt = new Date();

    try {
      let appPath: string;

      if (request.type === 'git') {
        appPath = await this.cloneFromGit(container.id, request);
      } else if (request.type === 'upload' && files) {
        appPath = await this.handleFileUpload(container.id, files);
      } else {
        throw new Error('Invalid deployment type or missing files');
      }

      await this.buildAndRunApplication(container, appPath, request);
      
      container.application.status = 'running';
      Logger.info(`Application deployed successfully`, { containerId: container.id });
    } catch (error) {
      container.application.status = 'error';
      container.application.buildLogs.push(`Error: ${error}`);
      Logger.error('Failed to deploy application', { containerId: container.id, error });
      throw error;
    }
  }

  private async cloneFromGit(containerId: string, request: DeployAppRequest): Promise<string> {
    if (!request.gitUrl) {
      throw new Error('Git URL is required');
    }

    const tempDir = path.join(config.upload.tempDir, containerId);
    await fs.mkdir(tempDir, { recursive: true });

    const git = simpleGit();
    await git.clone(request.gitUrl, tempDir, {
      '--branch': request.gitBranch || 'main',
      '--depth': '1',
    });

    Logger.info(`Cloned repository`, { containerId, gitUrl: request.gitUrl });
    return tempDir;
  }

  private async handleFileUpload(containerId: string, files: Express.Multer.File[]): Promise<string> {
    const tempDir = path.join(config.upload.tempDir, containerId);
    await fs.mkdir(tempDir, { recursive: true });

    // Save uploaded files
    for (const file of files) {
      const filePath = path.join(tempDir, file.originalname);
      await fs.writeFile(filePath, file.buffer);
    }

    Logger.info(`Files uploaded`, { containerId, fileCount: files.length });
    return tempDir;
  }

  private async buildAndRunApplication(
    container: Container,
    appPath: string,
    request: DeployAppRequest
  ): Promise<void> {
    const dockerContainer = this.docker.getContainer(`container-runner-${container.id}`);

    // Copy application files to container
    const tarStream = await this.createTarArchive(appPath);
    await dockerContainer.putArchive(tarStream, { path: '/workspace' });

    // Install dependencies
    const installCommand = 'cd /workspace && npm install';
    await this.executeCommand(dockerContainer, installCommand, container);

    // Build application if build command is provided
    if (request.buildCommand) {
      const buildCommand = `cd /workspace && ${request.buildCommand}`;
      await this.executeCommand(dockerContainer, buildCommand, container);
    }

    // Start application
    const startCommand = request.startCommand || 'npm start';
    const runCommand = `cd /workspace && PORT=${container.application?.port} ${startCommand}`;
    
    // Run application in background
    this.executeCommandBackground(dockerContainer, runCommand, container);

    // Open application in browser after a delay
    setTimeout(() => {
      this.openInBrowser(dockerContainer, container.application?.port || 3000);
    }, 5000);
  }

  private async createTarArchive(sourcePath: string): Promise<NodeJS.ReadableStream> {
    const archive = archiver('tar');
    
    archive.directory(sourcePath, false);
    archive.finalize();
    
    return archive;
  }

  private async executeCommand(
    dockerContainer: Docker.Container,
    command: string,
    container: Container
  ): Promise<void> {
    const exec = await dockerContainer.exec({
      Cmd: ['sh', '-c', command],
      AttachStdout: true,
      AttachStderr: true,
    });

    const stream = await exec.start({ hijack: true, stdin: false });
    
    return new Promise((resolve, reject) => {
      let output = '';
      
      stream.on('data', (chunk: Buffer) => {
        const data = chunk.toString();
        output += data;
        container.application?.buildLogs.push(data);
      });

      stream.on('end', () => {
        Logger.debug(`Command executed`, { command, output });
        resolve();
      });

      stream.on('error', (error) => {
        container.application?.buildLogs.push(`Error: ${error.message}`);
        reject(error);
      });
    });
  }

  private executeCommandBackground(
    dockerContainer: Docker.Container,
    command: string,
    container: Container
  ): void {
    dockerContainer.exec({
      Cmd: ['sh', '-c', command],
      AttachStdout: true,
      AttachStderr: true,
      Detach: true,
    }).then(exec => {
      return exec.start({ hijack: true, stdin: false });
    }).then(stream => {
      stream.on('data', (chunk: Buffer) => {
        const data = chunk.toString();
        container.application?.runtimeLogs.push(data);
      });
    }).catch(error => {
      Logger.error('Failed to start application', { containerId: container.id, error });
      if (container.application) {
        container.application.status = 'error';
        container.application.runtimeLogs.push(`Error: ${error.message}`);
      }
    });
  }

  private async openInBrowser(dockerContainer: Docker.Container, port: number): Promise<void> {
    const command = `DISPLAY=:1 google-chrome --no-sandbox --disable-dev-shm-usage http://localhost:${port}`;
    
    try {
      const exec = await dockerContainer.exec({
        Cmd: ['sh', '-c', command],
        AttachStdout: true,
        AttachStderr: true,
      });
      
      await exec.start({ hijack: true, stdin: false });
      Logger.info(`Opened application in browser`, { port });
    } catch (error) {
      Logger.error('Failed to open browser', { port, error });
    }
  }

  async stopApplication(container: Container): Promise<void> {
    if (!container.application) {
      throw new Error('No application deployed');
    }

    const dockerContainer = this.docker.getContainer(`container-runner-${container.id}`);
    
    // Kill Node.js processes
    const killCommand = 'pkill -f node || true';
    await this.executeCommand(dockerContainer, killCommand, container);
    
    container.application.status = 'stopped';
    container.updatedAt = new Date();
    
    Logger.info(`Application stopped`, { containerId: container.id });
  }

  async startApplication(container: Container, startCommand?: string): Promise<void> {
    if (!container.application) {
      throw new Error('No application deployed');
    }

    const dockerContainer = this.docker.getContainer(`container-runner-${container.id}`);
    const command = startCommand || 'npm start';
    const runCommand = `cd /workspace && PORT=${container.application.port} ${command}`;
    
    this.executeCommandBackground(dockerContainer, runCommand, container);
    
    container.application.status = 'running';
    container.updatedAt = new Date();
    
    Logger.info(`Application started`, { containerId: container.id });
  }
}
