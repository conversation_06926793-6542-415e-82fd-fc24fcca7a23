import { v4 as uuidv4 } from 'uuid';
import { Logger } from '@/utils/logger.js';
import { portManager } from '@/utils/portManager.js';
import type { Container, CreateContainerRequest, ContainerStats, VNCConnection } from '@/types/index.js';

/**
 * Mock Container Service for testing without Docker
 * This simulates container operations for development/demo purposes
 */
export class MockContainerService {
  private containers = new Map<string, Container>();

  async createContainer(request: CreateContainerRequest): Promise<Container> {
    const containerId = uuidv4();
    const vncPort = portManager.allocateVNCPort();
    const noVNCPort = portManager.allocateNoVNCPort();
    const appPort = portManager.allocateAppPort();

    const container: Container = {
      id: containerId,
      name: request.name,
      status: 'creating',
      nodeVersion: request.nodeVersion || '20',
      desktopEnvironment: request.desktopEnvironment || 'xfce',
      createdAt: new Date(),
      updatedAt: new Date(),
      ports: {
        vnc: vncPort,
        noVNC: noVNCPort,
        app: appPort,
      },
    };

    this.containers.set(containerId, container);

    // Simulate container creation delay
    setTimeout(() => {
      container.status = 'running';
      container.updatedAt = new Date();
      Logger.info(`Mock container created successfully`, { containerId, name: request.name });
    }, 2000);

    return container;
  }

  async getContainer(id: string): Promise<Container | null> {
    return this.containers.get(id) || null;
  }

  async listContainers(): Promise<Container[]> {
    return Array.from(this.containers.values());
  }

  async deleteContainer(id: string): Promise<void> {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    // Release ports
    portManager.releasePorts([
      container.ports.vnc,
      container.ports.noVNC,
      container.ports.app,
    ]);

    this.containers.delete(id);
    Logger.info(`Mock container deleted successfully`, { containerId: id });
  }

  async getContainerStats(id: string): Promise<ContainerStats> {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    // Return mock stats
    return {
      cpu: Math.random() * 50, // Random CPU usage 0-50%
      memory: Math.floor(Math.random() * 512 * 1024 * 1024), // Random memory usage up to 512MB
      network: {
        rx: Math.floor(Math.random() * 1024 * 1024), // Random RX bytes
        tx: Math.floor(Math.random() * 1024 * 1024), // Random TX bytes
      },
    };
  }

  getVNCConnection(id: string): VNCConnection {
    const container = this.containers.get(id);
    if (!container) {
      throw new Error('Container not found');
    }

    return {
      host: 'localhost',
      port: container.ports.vnc,
      webUrl: `http://localhost:${container.ports.noVNC}`,
    };
  }

  // Mock method to simulate application deployment
  async deployApplication(container: Container, type: 'upload' | 'git', options: any): Promise<void> {
    if (!container.application) {
      container.application = {
        name: 'demo-app',
        status: 'deploying',
        port: options.port || 3000,
        buildLogs: [],
        runtimeLogs: [],
      };
    }

    container.application.status = 'deploying';
    container.application.buildLogs = [
      'Starting deployment...',
      'Installing dependencies...',
      'npm install completed',
    ];

    // Simulate deployment process
    setTimeout(() => {
      if (container.application) {
        container.application.status = 'running';
        container.application.buildLogs.push('Build completed successfully');
        container.application.runtimeLogs = [
          'Application started',
          `Server listening on port ${container.application.port}`,
          'Ready to accept connections',
        ];
      }
      Logger.info(`Mock application deployed`, { containerId: container.id });
    }, 3000);
  }

  async startApplication(container: Container): Promise<void> {
    if (!container.application) {
      throw new Error('No application deployed');
    }

    container.application.status = 'running';
    container.application.runtimeLogs.push('Application restarted');
    container.updatedAt = new Date();
    
    Logger.info(`Mock application started`, { containerId: container.id });
  }

  async stopApplication(container: Container): Promise<void> {
    if (!container.application) {
      throw new Error('No application deployed');
    }

    container.application.status = 'stopped';
    container.application.runtimeLogs.push('Application stopped');
    container.updatedAt = new Date();
    
    Logger.info(`Mock application stopped`, { containerId: container.id });
  }
}
