import { config } from '@/config/index.js';

class PortManager {
  private usedPorts = new Set<number>();

  private getRandomPort(start: number, end: number): number {
    return Math.floor(Math.random() * (end - start + 1)) + start;
  }

  private isPortAvailable(port: number): boolean {
    return !this.usedPorts.has(port);
  }

  allocateVNCPort(): number {
    const { start, end } = config.ports.vnc;
    let attempts = 0;
    const maxAttempts = end - start + 1;

    while (attempts < maxAttempts) {
      const port = this.getRandomPort(start, end);
      if (this.isPortAvailable(port)) {
        this.usedPorts.add(port);
        return port;
      }
      attempts++;
    }

    throw new Error('No available VNC ports');
  }

  allocateNoVNCPort(): number {
    const { start, end } = config.ports.noVNC;
    let attempts = 0;
    const maxAttempts = end - start + 1;

    while (attempts < maxAttempts) {
      const port = this.getRandomPort(start, end);
      if (this.isPortAvailable(port)) {
        this.usedPorts.add(port);
        return port;
      }
      attempts++;
    }

    throw new Error('No available noVNC ports');
  }

  allocateAppPort(): number {
    const { start, end } = config.ports.app;
    let attempts = 0;
    const maxAttempts = end - start + 1;

    while (attempts < maxAttempts) {
      const port = this.getRandomPort(start, end);
      if (this.isPortAvailable(port)) {
        this.usedPorts.add(port);
        return port;
      }
      attempts++;
    }

    throw new Error('No available app ports');
  }

  releasePort(port: number): void {
    this.usedPorts.delete(port);
  }

  releasePorts(ports: number[]): void {
    ports.forEach(port => this.releasePort(port));
  }
}

export const portManager = new PortManager();
