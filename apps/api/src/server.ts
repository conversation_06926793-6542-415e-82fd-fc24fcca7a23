import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import morgan from 'morgan';
import { WebSocketServer } from 'ws';
import { createServer } from 'http';
import fs from 'fs/promises';
import path from 'path';

import { config } from '@/config/index.js';
import { Logger } from '@/utils/logger.js';
import containersRouter from '@/routes/containers.js';
import type { ApiResponse } from '@/types/index.js';

class Server {
  private app: express.Application;
  private server: any;
  private wss: WebSocketServer;

  constructor() {
    this.app = express();
    this.server = createServer(this.app);
    this.wss = new WebSocketServer({ server: this.server });
    
    this.setupMiddleware();
    this.setupRoutes();
    this.setupWebSocket();
    this.setupErrorHandling();
  }

  private setupMiddleware(): void {
    // Security middleware
    this.app.use(helmet({
      crossOriginEmbedderPolicy: false,
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'"],
          scriptSrc: ["'self'"],
          imgSrc: ["'self'", "data:", "https:"],
        },
      },
    }));

    // CORS configuration
    this.app.use(cors({
      origin: config.security.corsOrigins,
      credentials: true,
    }));

    // Compression and logging
    this.app.use(compression());
    this.app.use(morgan('combined'));

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }));
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }));
  }

  private setupRoutes(): void {
    // Health check
    this.app.get('/health', (req, res) => {
      const response: ApiResponse = {
        success: true,
        data: {
          status: 'healthy',
          timestamp: new Date().toISOString(),
          uptime: process.uptime(),
        },
      };
      res.json(response);
    });

    // API routes
    this.app.use('/api/containers', containersRouter);

    // 404 handler
    this.app.use('*', (req, res) => {
      const response: ApiResponse = {
        success: false,
        error: 'Route not found',
      };
      res.status(404).json(response);
    });
  }

  private setupWebSocket(): void {
    this.wss.on('connection', (ws, req) => {
      Logger.info('WebSocket connection established', { 
        ip: req.socket.remoteAddress,
        userAgent: req.headers['user-agent'],
      });

      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          Logger.debug('WebSocket message received', { data });
          
          // Handle different message types
          switch (data.type) {
            case 'subscribe':
              // Subscribe to container updates
              ws.send(JSON.stringify({
                type: 'subscribed',
                containerId: data.containerId,
                timestamp: new Date(),
              }));
              break;
            
            case 'ping':
              ws.send(JSON.stringify({
                type: 'pong',
                timestamp: new Date(),
              }));
              break;
            
            default:
              Logger.warn('Unknown WebSocket message type', { type: data.type });
          }
        } catch (error) {
          Logger.error('Failed to parse WebSocket message', { error, message });
        }
      });

      ws.on('close', () => {
        Logger.info('WebSocket connection closed');
      });

      ws.on('error', (error) => {
        Logger.error('WebSocket error', { error });
      });

      // Send welcome message
      ws.send(JSON.stringify({
        type: 'welcome',
        message: 'Connected to Container Runner API',
        timestamp: new Date(),
      }));
    });
  }

  private setupErrorHandling(): void {
    // Global error handler
    this.app.use((error: Error, req: express.Request, res: express.Response, next: express.NextFunction) => {
      Logger.error('Unhandled error', { 
        error: error.message,
        stack: error.stack,
        url: req.url,
        method: req.method,
      });

      const response: ApiResponse = {
        success: false,
        error: process.env.NODE_ENV === 'production' 
          ? 'Internal server error' 
          : error.message,
      };

      res.status(500).json(response);
    });

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      Logger.error('Uncaught exception', { error });
      process.exit(1);
    });

    // Handle unhandled promise rejections
    process.on('unhandledRejection', (reason, promise) => {
      Logger.error('Unhandled promise rejection', { reason, promise });
      process.exit(1);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      Logger.info('SIGTERM received, shutting down gracefully');
      this.shutdown();
    });

    process.on('SIGINT', () => {
      Logger.info('SIGINT received, shutting down gracefully');
      this.shutdown();
    });
  }

  private async ensureDirectories(): Promise<void> {
    try {
      await fs.mkdir(config.upload.tempDir, { recursive: true });
      Logger.info('Upload directory created', { dir: config.upload.tempDir });
    } catch (error) {
      Logger.error('Failed to create upload directory', { error });
    }
  }

  async start(): Promise<void> {
    try {
      await this.ensureDirectories();
      
      this.server.listen(config.port, config.host, () => {
        Logger.info(`Server started`, {
          host: config.host,
          port: config.port,
          env: process.env.NODE_ENV || 'development',
        });
      });
    } catch (error) {
      Logger.error('Failed to start server', { error });
      process.exit(1);
    }
  }

  private shutdown(): void {
    Logger.info('Shutting down server...');
    
    this.server.close(() => {
      Logger.info('HTTP server closed');
      
      this.wss.close(() => {
        Logger.info('WebSocket server closed');
        process.exit(0);
      });
    });

    // Force close after 10 seconds
    setTimeout(() => {
      Logger.error('Could not close connections in time, forcefully shutting down');
      process.exit(1);
    }, 10000);
  }

  // Method to broadcast messages to all connected WebSocket clients
  broadcast(message: any): void {
    const messageStr = JSON.stringify(message);
    this.wss.clients.forEach((client) => {
      if (client.readyState === client.OPEN) {
        client.send(messageStr);
      }
    });
  }
}

// Start the server
const server = new Server();
server.start().catch((error) => {
  Logger.error('Failed to start application', { error });
  process.exit(1);
});

export default server;
