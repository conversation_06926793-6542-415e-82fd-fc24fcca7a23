export interface Container {
  id: string;
  name: string;
  status: 'creating' | 'running' | 'stopped' | 'error';
  nodeVersion: string;
  desktopEnvironment: 'xfce' | 'lxde';
  createdAt: Date;
  updatedAt: Date;
  ports: {
    vnc: number;
    noVNC: number;
    app: number;
  };
  application?: {
    name: string;
    status: 'deploying' | 'running' | 'stopped' | 'error';
    port: number;
    buildLogs: string[];
    runtimeLogs: string[];
  };
}

export interface CreateContainerRequest {
  name: string;
  nodeVersion?: string;
  desktopEnvironment?: 'xfce' | 'lxde';
}

export interface DeployAppRequest {
  type: 'upload' | 'git';
  gitUrl?: string;
  gitBranch?: string;
  buildCommand?: string;
  startCommand?: string;
  port?: number;
}

export interface ContainerStats {
  cpu: number;
  memory: number;
  network: {
    rx: number;
    tx: number;
  };
}

export interface VNCConnection {
  host: string;
  port: number;
  password?: string;
  webUrl: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface WebSocketMessage {
  type: 'container_status' | 'build_log' | 'runtime_log' | 'error';
  containerId: string;
  data: any;
  timestamp: Date;
}
